import asyncio
import logging
import os
import time
from pathlib import Path

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.messages import Message
from autogen_core.memory import MemoryContent, MemoryMimeType
from autogen_ext.models.openai import OpenAIChatCompletionClient

from ai_backend_system.utils.memory.chromadb_hierarchical_memory import (
    ChromaDBHierarchicalMemory,
    ChromaDBHierarchicalMemoryConfig,
    MemoryLevel
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建缓存目录
CACHE_DIR = os.path.join(str(Path.home()), ".chromadb_autogen_hierarchy")
os.makedirs(CACHE_DIR, exist_ok=True)


async def main():
    # 创建LLM客户端
    model_client = OpenAIChatCompletionClient(
        model="gemini-2.0-flash-exp",
        api_key=os.getenv('GEMINI_API_KEY'),
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "unknown",
        }
    )
    
    # 创建层次化记忆配置
    memory_config = ChromaDBHierarchicalMemoryConfig(
        collection_name="user_knowledge",
        persistence_path=CACHE_DIR,
        short_term_k=5,
        medium_term_k=3,
        long_term_k=2,
        short_term_score_threshold=0.3,
        medium_term_score_threshold=0.4,
        long_term_score_threshold=0.5,
        # 为了演示，设置较短的时间阈值
        short_to_medium_threshold=300,  # 5分钟
        medium_to_long_threshold=600,   # 10分钟
        enable_memory_compression=True,
        llm_client=model_client,
        maintenance_interval=5  # 每5次操作进行一次维护
    )
    
    # 创建层次化记忆实例
    hierarchical_memory = ChromaDBHierarchicalMemory(config=memory_config)
    
    # 创建助手代理
    assistant = AssistantAgent(
        name="Assistant",
        system_message="You are a helpful AI assistant with hierarchical memory. Use your memory to provide relevant, consistent information.",
        model_client=model_client,
        memory=hierarchical_memory
    )
    
    # 创建用户代理
    user_proxy = UserProxyAgent(
        name="User",
        memory=hierarchical_memory
    )
    
    # 添加一些初始记忆 - 每个级别的一些样本
    current_time = time.time()
    
    # 短期记忆 - 最近的对话内容
    await hierarchical_memory.add(
        MemoryContent(
            content="The user prefers information in metric units.",
            mime_type=MemoryMimeType.TEXT,
            metadata={
                "memory_level": MemoryLevel.SHORT_TERM,
                "importance": 0.8,
                "created_at": current_time - 60,
                "category": "preferences"
            }
        )
    )
    
    await hierarchical_memory.add(
        MemoryContent(
            content="The user is planning a trip to Japan next month.",
            mime_type=MemoryMimeType.TEXT,
            metadata={
                "memory_level": MemoryLevel.SHORT_TERM,
                "importance": 0.7,
                "created_at": current_time - 120,
                "category": "plans"
            }
        )
    )
    
    # 中期记忆 - 几天前的知识
    await hierarchical_memory.add(
        MemoryContent(
            content="The user is vegetarian and prefers plant-based meal options.",
            mime_type=MemoryMimeType.TEXT,
            metadata={
                "memory_level": MemoryLevel.MEDIUM_TERM,
                "importance": 0.9,
                "created_at": current_time - 400,  # 模拟早些时候添加的
                "category": "preferences"
            }
        )
    )
    
    # 长期记忆 - 持久的用户知识
    await hierarchical_memory.add(
        MemoryContent(
            content="The user has a background in computer science and is interested in AI and machine learning.",
            mime_type=MemoryMimeType.TEXT,
            metadata={
                "memory_level": MemoryLevel.LONG_TERM,
                "importance": 0.9,
                "created_at": current_time - 700,  # 模拟很久之前添加的
                "category": "background"
            }
        )
    )
    
    # 模拟一个对话
    logger.info("Starting conversation with hierarchical memory...")
    
    # 第一个问题 - 应该考虑用户的饮食习惯
    await user_proxy.send(
        Message(
            content="Can you suggest some good restaurants for me to try?",
            sender=user_proxy.name,
            recipient=assistant.name
        ),
        assistant
    )
    
    # 添加一些新的短期记忆，模拟在对话过程中捕获的信息
    await hierarchical_memory.add(
        MemoryContent(
            content="The user mentioned they enjoy spicy food.",
            mime_type=MemoryMimeType.TEXT,
            metadata={
                "memory_level": MemoryLevel.SHORT_TERM,
                "importance": 0.6,
                "created_at": current_time,
                "category": "preferences"
            }
        )
    )
    
    # 第二个问题 - 应该考虑用户在旅行和喜好辛辣食物的情况
    await user_proxy.send(
        Message(
            content="I'm looking for travel tips for my Japan trip. Any advice on local cuisine?",
            sender=user_proxy.name,
            recipient=assistant.name
        ),
        assistant
    )
    
    # 运行维护来促进记忆
    logger.info("Running memory maintenance to simulate passage of time...")
    
    # 手动更改一些记忆的时间戳，模拟时间流逝
    old_memories = await hierarchical_memory.query("", memory_level=MemoryLevel.SHORT_TERM)
    for memory in old_memories.results:
        if "user prefers information in metric units" in str(memory.content):
            if memory.metadata:
                # 设置为更早的时间，促使其被提升到中期记忆
                memory.metadata["created_at"] = current_time - 400
                # 在实际应用中，我们需要重新添加这个修改过的记忆
    
    # 执行维护
    await hierarchical_memory._perform_maintenance()
    
    # 查询每个级别的记忆以显示状态
    short_memories = await hierarchical_memory.query("", memory_level=MemoryLevel.SHORT_TERM)
    medium_memories = await hierarchical_memory.query("", memory_level=MemoryLevel.MEDIUM_TERM)
    long_memories = await hierarchical_memory.query("", memory_level=MemoryLevel.LONG_TERM)
    
    logger.info(f"Short-term memories: {len(short_memories.results)}")
    for memory in short_memories.results:
        logger.info(f"  - {memory.content}")
    
    logger.info(f"Medium-term memories: {len(medium_memories.results)}")
    for memory in medium_memories.results:
        logger.info(f"  - {memory.content}")
    
    logger.info(f"Long-term memories: {len(long_memories.results)}")
    for memory in long_memories.results:
        logger.info(f"  - {memory.content}")
    
    # 第三个问题 - 测试跨级别的记忆检索
    await user_proxy.send(
        Message(
            content="What do you know about me and my interests?",
            sender=user_proxy.name,
            recipient=assistant.name
        ),
        assistant
    )
    
    # 清理资源
    logger.info("Cleaning up resources...")
    await hierarchical_memory.close()
    await model_client.close()


if __name__ == "__main__":
    asyncio.run(main()) 