#!/usr/bin/env python3
# ai-backend-system/main.py
"""
AI 后端系统 (ai-backend-system) 的程序入口模块 (main.py)

该模块是 AI 后端系统的启动点，负责：
1. 加载系统配置 (从 config/config.yaml 文件)
2. 初始化日志系统
3. 检查必要的环境变量
4. 加载 Flask 应用 (从 services/wecom/callback.py)
5. 支持开发模式 (Flask 内置服务器) 和生产模式 (Gunicorn)
6. 确保与企业微信回调服务和智能客服逻辑无缝集成

程序启动流程:
1. 运行 `python main.py` 命令启动开发模式，或通过 Gunicorn 启动生产模式
2. 加载配置文件 (config/config.yaml)
3. 初始化日志系统
4. 检查必要的环境变量
5. 加载 Flask 应用 (从 services/wecom/callback.py)
6. 根据运行模式启动服务器
   - 开发模式: 使用 Flask 内置服务器
   - 生产模式: 使用 Gunicorn (通过 gunicorn main:app 命令)
"""
import os
import sys
import logging
import argparse
import socket
import traceback

# 确保 ai-backend-system 目录在 Python 路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# 导入配置加载器
from config import get_config

# 创建日志目录
log_dir = os.path.join(current_dir, "logs")
os.makedirs(log_dir, exist_ok=True)

# 初始化日志系统
def setup_logging():
    """
    初始化日志系统

    从配置文件加载日志设置，并配置日志系统
    """
    try:
        # 从配置文件加载日志设置
        config = get_config()
        # 使用直接字典访问，并提供默认值
        try:
            logging_config = config['logging']
        except KeyError:
            logging_config = {}

        try:
            log_level = logging_config['log_level'].upper()
        except (KeyError, AttributeError):
            log_level = "INFO"

        try:
            log_file_path = logging_config['log_file_path']
        except KeyError:
            log_file_path = os.path.join(log_dir, "ai_backend_system.log")

        try:
            log_format = logging_config['log_format']
        except KeyError:
            log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"

        # 确保日志目录存在
        os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

        # 配置根日志记录器
        logging.basicConfig(
            level=getattr(logging, log_level),
            format=log_format,
            handlers=[
                logging.FileHandler(log_file_path),
                logging.StreamHandler()  # 同时输出到控制台
            ]
        )

        # 设置第三方库的日志级别
        logging.getLogger("werkzeug").setLevel(logging.WARNING)
        logging.getLogger("urllib3").setLevel(logging.WARNING)

        logger = logging.getLogger(__name__)
        logger.info(f"日志系统初始化完成，日志级别: {log_level}, 日志文件: {log_file_path}")
        return logger
    except Exception as e:
        # 如果配置加载失败，使用默认配置
        print(f"警告: 日志配置加载失败: {e}")
        print(traceback.format_exc())

        # 配置默认日志
        default_log_file = os.path.join(log_dir, "ai_backend_system.log")
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
            handlers=[
                logging.FileHandler(default_log_file),
                logging.StreamHandler()
            ]
        )

        logger = logging.getLogger(__name__)
        logger.warning(f"使用默认日志配置，日志文件: {default_log_file}")
        return logger

# 检查环境变量和配置
def check_environment(logger):
    """
    检查必要的环境变量和配置

    确保所有必要的环境变量和配置都已正确设置

    Args:
        logger: 日志记录器

    Returns:
        bool: 环境检查是否通过
    """
    try:
        # 加载企业微信配置
        config = get_config()

        # 检查wecom配置是否存在
        try:
            wecom_config = config['wecom']
        except KeyError:
            logger.error("缺少企业微信配置部分")
            return False

        # 检查必要的配置项
        required_configs = ['Token', 'EncodingAESKey', 'CorpID', 'Secret', 'AgentID']
        missing_configs = [config_item for config_item in required_configs if config_item not in wecom_config]

        if missing_configs:
            logger.error(f"缺少必要的企业微信配置: {', '.join(missing_configs)}")
            return False

        # 检查配置值是否有效
        for config_item in required_configs:
            try:
                if not wecom_config[config_item]:
                    logger.error(f"企业微信配置 '{config_item}' 的值无效或为空")
                    return False
            except KeyError:
                logger.error(f"企业微信配置 '{config_item}' 不存在")
                return False

        logger.info("环境变量和配置检查通过")
        return True
    except Exception as e:
        logger.error(f"环境检查失败: {e}")
        logger.error(traceback.format_exc())
        return False

# 获取本机IP地址
def get_local_ip():
    """
    获取本机IP地址

    Returns:
        str: 本机IP地址
    """
    try:
        # 创建一个临时socket连接来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

# 加载Flask应用
def load_flask_app(logger):
    """
    从services/wecom/callback.py加载Flask应用

    Args:
        logger: 日志记录器

    Returns:
        Flask: Flask应用实例
    """
    try:
        # 导入Flask应用
        from services.wecom.callback import app
        logger.info("Flask应用加载成功")
        return app
    except ImportError as e:
        logger.error(f"无法导入Flask应用: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)

# 解析命令行参数
def parse_arguments():
    """
    解析命令行参数

    Returns:
        argparse.Namespace: 解析后的命令行参数
    """
    parser = argparse.ArgumentParser(description='AI后端系统启动脚本')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, help='服务器端口 (默认: 5000)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--ssl', action='store_true', help='启用SSL (仅开发模式)')
    return parser.parse_args()

# 主函数
def main():
    """
    主函数，程序入口点
    """
    # 解析命令行参数
    args = parse_arguments()

    # 初始化日志系统
    logger = setup_logging()
    logger.info("AI后端系统启动中...")

    # 检查环境变量和配置
    if not check_environment(logger):
        logger.error("环境检查失败，程序退出")
        sys.exit(1)

    # 加载Flask应用
    app = load_flask_app(logger)

    # 获取本机IP地址
    local_ip = get_local_ip()

    # 打印回调URL
    protocol = "https" if args.ssl else "http"
    callback_url = f"{protocol}://{local_ip}:{args.port}/wecom/callback"
    logger.info(f"企业微信回调URL: {callback_url}")
    print(f"\n============================================================")
    print(f"企业微信回调URL: {callback_url}")
    print(f"请在企业微信管理后台配置此URL作为回调地址")
    print(f"============================================================\n")

    # 判断运行环境
    if os.environ.get('GUNICORN_CMD_ARGS') is not None:
        # 生产模式 (Gunicorn)
        logger.info("在生产模式下运行 (Gunicorn)")
        # 在生产模式下，Gunicorn会负责启动应用，这里不需要额外操作
    else:
        # 开发模式 (Flask内置服务器)
        logger.info(f"在开发模式下运行 (Flask内置服务器), 主机: {args.host}, 端口: {args.port}, 调试模式: {args.debug}")

        # 启动Flask内置服务器
        ssl_context = 'adhoc' if args.ssl else None
        app.run(
            host=args.host,
            port=args.port,
            debug=args.debug,
            ssl_context=ssl_context
        )

# 导出Flask应用实例 (用于Gunicorn)
from services.wecom.callback import app

# 程序入口点
if __name__ == "__main__":
    main()