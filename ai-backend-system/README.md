# AI Backend System

## Directory Structure

```mermaid
graph LR
    Root[ai-backend-system/] --> Agents[agents/]
    Root --> Utils[utils/]
    Root --> RAG[rag_knowledge_base/]
    Root --> Config[config/]
    Root --> DataModels[data_models/]
    Root --> Services[services/]
    Root --> API[api/]
    Root --> MainFiles[Root Files]

    Agents --> A1[operational_manager_agent.py]
    Agents --> A2[data_processing_agent.py]
    Agents --> A3[content_generation_agent.py]
    Agents --> A4[content_review_agent.py]
    Agents --> A5[__init__.py]

    Utils --> U1[scrapy_util.py]
    Utils --> U2[data_processing_util.py]
    Utils --> U3[wordpress_util.py]
    Utils --> U4[db_connector.py]
    Utils --> U5[__init__.py]

    RAG --> R1[scraper.py]
    RAG --> R2[indexer.py]
    RAG --> R3[retriever.py]
    RAG --> R4[__init__.py]

    Config --> C1[config.yaml]
    Config --> C2[__init__.py]

    DataModels --> D1[product_data.py]
    DataModels --> D2[deal_data.py]
    DataModels --> D3[__init__.py]

    Services --> S1[deal_service.py]
    Services --> S2[article_service.py]
    Services --> S3[__init__.py]

    API --> P1[deal_api.py]
    API --> P2[article_api.py]
    API --> P3[__init__.py]

    MainFiles --> M1[main.py]
    MainFiles --> M2[requirements.txt]
    MainFiles --> M3[README.md]

    style Root fill:#f9f,stroke:#333,stroke-width:2px
    style Agents fill:#bbf,stroke:#333,stroke-width:1px
    style Utils fill:#bfb,stroke:#333,stroke-width:1px
    style RAG fill:#fbf,stroke:#333,stroke-width:1px
    style Config fill:#ffb,stroke:#333,stroke-width:1px
    style DataModels fill:#bff,stroke:#333,stroke-width:1px
    style Services fill:#fbb,stroke:#333,stroke-width:1px
    style API fill:#bbb,stroke:#333,stroke-width:1px
```

## Project Structure

### agents/
Agent definitions directory (Autogen Agents)
- `operational_manager_agent.py` - Operations Manager Agent (Orchestrator)
- `data_processing_agent.py` - Data Processing Agent
- `content_generation_agent.py` - Content Generation Agent
- `content_review_agent.py` - Content Review Agent
- `customer_service_team/` - 客服团队代理
  - `teaching_assistant_agent.py` - 教学助手代理（处理编程问题）
- `__init__.py`

### utils/
Utility modules directory
- `scrapy_util.py` - Scrapy Utility (Scrapy wrapper)
- `data_processing_util.py` - Data Processing Utility
- `wordpress_util.py` - WordPress Utility (REST API wrapper)
- `db_connector.py` - Database Connection Utility (Supabase)
- `__init__.py`

### rag_knowledge_base/
RAG Knowledge Base related modules
- `scraper.py` - Knowledge Base Data Scraping Module (Google Search API)
- `indexer.py` - Knowledge Base Vector Index Building Module
- `retriever.py` - Knowledge Base Retrieval Module
- `__init__.py`

### config/
Configuration directory
- `config.yaml` - Configuration File (API keys, DB settings, etc.)
- `__init__.py`

### data_models/
Data Model Definitions (Optional, for stricter data type definitions)
- `product_data.py`
- `deal_data.py`
- `__init__.py`

### services/
Service Layer (Business Logic Orchestration)
- `deal_service.py` - Deal Related Business Logic (Scraping, Processing, Publishing)
- `article_service.py` - Article Related Business Logic (Generation, Publishing)
- `wecom/` - 企业微信集成服务
  - `callback.py` - 企业微信回调处理服务
  - `client.py` - 企业微信API客户端
  - `crypto.py` - 企业微信消息加解密
  - `message_handler.py` - 企业微信消息处理
- `__init__.py`

### api/
API Interface Definitions (If AI backend needs to provide external APIs)
- `deal_api.py`
- `article_api.py`
- `__init__.py`

### Root Files
- `main.py` - Program Entry (e.g., Task Scheduling, API Server Startup)
- `requirements.txt` - Python Dependencies
- `README.md`

## Framework Description

### agents/
Contains Agent code defined using the Autogen framework. Each Agent file corresponds to a role, e.g., `operational_manager_agent.py` defines the Operations Manager Agent's behavior and logic, responsible for orchestrating the entire business process.

### utils/
Contains programmatic utility modules:
- `scrapy_util.py`: Encapsulates Scrapy crawler tools, providing methods to start crawlers and retrieve data
- `data_processing_util.py`: Contains common data processing functions like cleaning, format conversion, and structured processing
- `wordpress_util.py`: Encapsulates WordPress REST API tools, providing methods for publishing articles and getting user comments
- `db_connector.py`: Database connection utility class for connecting to Supabase PostgreSQL and Vector databases

### rag_knowledge_base/
Contains RAG knowledge base construction and retrieval modules:
- `scraper.py`: Module for scraping review articles using Google Search API
- `indexer.py`: Module for building vector indices and storing article content vectors in Supabase Vector database
- `retriever.py`: Module for retrieving relevant review articles from vector database for RAG retrieval

### config/
Configuration files, e.g., `config.yaml` stores API keys (OpenAI, Gemini, ScraperAPI, Google Search API), database connection info (Supabase), and other system parameters.

### data_models/
(Optional) For stricter data type definitions and validation, define data model classes using Pydantic or other validation libraries.

### services/
Service layer for orchestrating business logic. For example, `deal_service.py` contains complete business processes for Deal information automated scraping and publishing.

### api/
(Optional) If AI backend needs to provide external APIs (e.g., for other systems or direct frontend calls), define API interfaces here using Flask, FastAPI, etc.

### main.py
AI backend system entry file. 主要功能：
1. 初始化日志系统
2. 加载配置文件
3. 启动企业微信回调服务器
   - 开发模式：使用Flask内置服务器
   - 生产模式：使用Gunicorn
4. 支持任务调度（如定时抓取Deal信息）

### requirements.txt
Python dependency file listing all required Python libraries and versions for easy installation using `pip install -r requirements.txt`.

## Module Interaction and Data Flow

### Content Domain (AI Backend) → Product & User Domain (WordPress)
- Primary data push through `utils/wordpress_util.py` which encapsulates WordPress REST API
- Examples include publishing Deal information and review articles

### Product & User Domain (WordPress) → Content Domain (AI Backend)
- WordPress system can provide data query interfaces to AI backend through REST API
- Requires definition in `includes/rest-api.php`
- Use cases: AI Agents retrieving product category or user information
- Note: This reverse calling pattern is less frequent in current architecture

### AI Backend System Internal Module Interaction
- Primary interaction through:
  - Agent collaboration (using Autogen framework)
  - Agent calling Utils modules (direct function calls)

### AI Backend System → Supabase Database
- Database operations through `utils/db_connector.py` module
- Supports both PostgreSQL and Vector database operations

## 运行应用

### 配置企业微信

在 `config/config.yaml` 中配置企业微信相关参数：

```yaml
wecom:
  Token: "your_token"
  EncodingAESKey: "your_encoding_aes_key"
  CorpID: "your_corp_id"
  Secret: "your_secret"
  AgentID: "your_agent_id"
```

### 开发模式

使用 Flask 内置服务器运行（适合开发和测试）：

```bash
cd ai-backend-system
python main.py --host 0.0.0.0 --port 5000 --ssl
```

参数说明：
- `--host`: 服务器主机地址，默认为 0.0.0.0（监听所有网络接口）
- `--port`: 服务器端口，默认为 5000
- `--debug`: 启用调试模式
- `--ssl`: 启用 SSL（使用自签名证书）

### 生产模式

使用 Gunicorn 运行（适合生产环境）：

```bash
cd ai-backend-system
gunicorn main:app -b 0.0.0.0:5000 -w 4 --timeout 120
```

参数说明：
- `-b`: 绑定地址和端口
- `-w`: 工作进程数
- `--timeout`: 请求超时时间（秒）

### 与 Nginx 集成

在生产环境中，建议使用 Nginx 作为反向代理，提供 HTTPS 支持：

```nginx
server {
    listen 443 ssl;
    server_name your_domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 企业微信回调配置

1. 登录企业微信管理后台
2. 进入应用管理 -> 自建应用 -> 选择你的应用
3. 点击"接收消息"设置
4. 配置接收消息的URL为：`https://your_domain.com/wecom/callback`
5. 填写Token和EncodingAESKey（与配置文件中一致）
6. 点击"保存"