"""
企业微信 API 客户端（调用主动接口）。实现企业微信主动 API 调用（如发送消息、获取用户信息），参考 企业微信 API 文档
此文件实现企业微信主动 API 调用，用于发送消息（如智能客服回复）、获取用户信息等。它通过 access_token 与企业微信服务器通信。
使用 requests 库调用企业微信 API，获取 access_token 并缓存（有效期 7200 秒）。
实现 send_text_message 方法，发送智能客服回复（需 agent_id，在企业微信后台配置）。
实现 get_user_info 方法，获取用户信息（如用户部门、角色）。
"""

import requests
import json
import logging
import time
import threading
from config import get_config

# 配置日志
logger = logging.getLogger(__name__)

class WeComClient:
    """
    企业微信API客户端

    用于调用企业微信的主动API接口，如发送消息、获取用户信息等。
    自动管理access_token的获取和刷新。
    """
    # 类级别的锁，用于防止多线程同时刷新token
    _token_lock = threading.Lock()

    def __init__(self):
        """
        初始化企业微信API客户端

        从配置文件中读取企业微信的配置信息，包括企业ID、应用密钥等
        """
        wecom_config = get_config()['wecom']
        self.corp_id = wecom_config['CorpID']
        self.secret = wecom_config['Secret']
        self.base_url = "https://qyapi.weixin.qq.com/cgi-bin"
        self.access_token = None
        self.token_expiry = 0
        self.retry_count = 3  # 请求失败时的重试次数
        self.retry_delay = 1  # 重试间隔（秒）

        logger.info(f"WeComClient initialized with CorpID: {self.corp_id}")

    def get_access_token(self):
        """
        获取企业微信API的access_token

        如果当前token未过期，则直接返回；否则请求新token
        使用线程锁确保多线程环境下的安全

        Returns:
            str: 有效的access_token

        Raises:
            ValueError: 获取token失败时抛出
        """
        # 检查token是否有效
        if self.access_token and self.token_expiry > time.time():
            return self.access_token

        # 使用线程锁防止多线程同时刷新token
        with self._token_lock:
            # 双重检查，防止在等待锁的过程中token已被其他线程刷新
            if self.access_token and self.token_expiry > time.time():
                return self.access_token

            url = f"{self.base_url}/gettoken?corpid={self.corp_id}&corpsecret={self.secret}"

            for retry in range(self.retry_count):
                try:
                    logger.debug(f"Requesting new access_token, attempt {retry+1}/{self.retry_count}")
                    response = requests.get(url, timeout=5)
                    response.raise_for_status()
                    data = response.json()

                    if data.get('errcode') == 0:
                        self.access_token = data['access_token']
                        # 提前60秒过期，确保有足够时间刷新
                        self.token_expiry = time.time() + data['expires_in'] - 60
                        logger.info(f"Access token refreshed successfully, expires in {data['expires_in']} seconds")
                        return self.access_token
                    else:
                        error_msg = f"Failed to get access_token: {data.get('errmsg', 'Unknown error')}"
                        logger.error(error_msg)

                        # 如果是最后一次重试，则抛出异常
                        if retry == self.retry_count - 1:
                            raise ValueError(error_msg)

                        # 否则等待后重试
                        time.sleep(self.retry_delay)

                except requests.RequestException as e:
                    logger.error(f"Network error when fetching access_token: {e}")

                    # 如果是最后一次重试，则抛出异常
                    if retry == self.retry_count - 1:
                        raise ValueError(f"Failed to get access_token after {self.retry_count} attempts: {e}")

                    # 否则等待后重试
                    time.sleep(self.retry_delay)

            # 如果所有重试都失败，抛出异常
            raise ValueError(f"Failed to get access_token after {self.retry_count} attempts")

    def send_text_message(self, to_user, content, agent_id):
        """
        发送文本消息

        Args:
            to_user (str): 接收消息的用户ID，多个用户用'|'分隔
            content (str): 消息内容
            agent_id (int/str): 企业应用ID

        Returns:
            dict: 企业微信API的响应数据

        Raises:
            ValueError: 发送消息失败时抛出
        """
        url = f"{self.base_url}/message/send?access_token={self.get_access_token()}"

        # 确保agent_id是整数
        try:
            agent_id = int(agent_id)
        except (ValueError, TypeError):
            logger.error(f"Invalid agent_id: {agent_id}, must be an integer")
            raise ValueError(f"Invalid agent_id: {agent_id}, must be an integer")

        payload = {
            "touser": to_user,
            "msgtype": "text",
            "agentid": agent_id,
            "text": {"content": content},
            "safe": 0  # 0表示非保密消息，1表示保密消息
        }

        for retry in range(self.retry_count):
            try:
                logger.debug(f"Sending text message to {to_user}, attempt {retry+1}/{self.retry_count}")
                response = requests.post(url, json=payload, timeout=5)
                response.raise_for_status()
                data = response.json()

                if data.get('errcode') == 0:
                    logger.info(f"Successfully sent text message to {to_user}")
                    return data
                elif data.get('errcode') == 40014:  # invalid access_token
                    logger.warning("Access token expired, refreshing...")
                    self.access_token = None  # 强制刷新token
                    url = f"{self.base_url}/message/send?access_token={self.get_access_token()}"
                    # 继续下一次重试
                else:
                    error_msg = f"Failed to send message: {data.get('errmsg', 'Unknown error')}"
                    logger.error(error_msg)

                    # 如果是最后一次重试，则抛出异常
                    if retry == self.retry_count - 1:
                        raise ValueError(error_msg)

                    # 否则等待后重试
                    time.sleep(self.retry_delay)

            except requests.RequestException as e:
                logger.error(f"Network error when sending message: {e}")

                # 如果是最后一次重试，则抛出异常
                if retry == self.retry_count - 1:
                    raise ValueError(f"Failed to send message after {self.retry_count} attempts: {e}")

                # 否则等待后重试
                time.sleep(self.retry_delay)

        # 如果所有重试都失败，抛出异常
        raise ValueError(f"Failed to send message after {self.retry_count} attempts")

    def get_user_info(self, user_id):
        """
        获取用户信息

        Args:
            user_id (str): 用户ID

        Returns:
            dict: 用户信息

        Raises:
            ValueError: 获取用户信息失败时抛出
        """
        url = f"{self.base_url}/user/get?access_token={self.get_access_token()}&userid={user_id}"

        for retry in range(self.retry_count):
            try:
                logger.debug(f"Getting user info for {user_id}, attempt {retry+1}/{self.retry_count}")
                response = requests.get(url, timeout=5)
                response.raise_for_status()
                data = response.json()

                if data.get('errcode') == 0:
                    logger.info(f"Successfully retrieved user info for {user_id}")
                    return data
                elif data.get('errcode') == 40014:  # invalid access_token
                    logger.warning("Access token expired, refreshing...")
                    self.access_token = None  # 强制刷新token
                    url = f"{self.base_url}/user/get?access_token={self.get_access_token()}&userid={user_id}"
                    # 继续下一次重试
                else:
                    error_msg = f"Failed to get user info: {data.get('errmsg', 'Unknown error')}"
                    logger.error(error_msg)

                    # 如果是最后一次重试，则抛出异常
                    if retry == self.retry_count - 1:
                        raise ValueError(error_msg)

                    # 否则等待后重试
                    time.sleep(self.retry_delay)

            except requests.RequestException as e:
                logger.error(f"Network error when getting user info: {e}")

                # 如果是最后一次重试，则抛出异常
                if retry == self.retry_count - 1:
                    raise ValueError(f"Failed to get user info after {self.retry_count} attempts: {e}")

                # 否则等待后重试
                time.sleep(self.retry_delay)

        # 如果所有重试都失败，抛出异常
        raise ValueError(f"Failed to get user info after {self.retry_count} attempts")

    def get_media(self, media_id):
        """
        获取临时素材（图片、语音、视频、文件）

        Args:
            media_id (str): 媒体文件ID

        Returns:
            tuple: (media_data, content_type) 媒体文件数据和内容类型

        Raises:
            ValueError: 获取媒体文件失败时抛出
        """
        url = f"{self.base_url}/media/get?access_token={self.get_access_token()}&media_id={media_id}"

        for retry in range(self.retry_count):
            try:
                logger.debug(f"Getting media file with ID: {media_id}, attempt {retry+1}/{self.retry_count}")
                response = requests.get(url, timeout=10)  # 增加超时时间，因为媒体文件可能较大
                response.raise_for_status()

                # 检查是否是JSON响应（通常表示错误）
                content_type = response.headers.get('Content-Type', '')
                if 'application/json' in content_type or 'text/plain' in content_type:
                    try:
                        data = response.json()
                        if data.get('errcode', 0) != 0:
                            error_msg = f"Failed to get media: {data.get('errmsg', 'Unknown error')}"
                            logger.error(error_msg)

                            # 如果是token过期，刷新token后重试
                            if data.get('errcode') == 40014:  # invalid access_token
                                logger.warning("Access token expired, refreshing...")
                                self.access_token = None  # 强制刷新token
                                url = f"{self.base_url}/media/get?access_token={self.get_access_token()}&media_id={media_id}"
                                continue

                            # 如果是最后一次重试，则抛出异常
                            if retry == self.retry_count - 1:
                                raise ValueError(error_msg)

                            # 否则等待后重试
                            time.sleep(self.retry_delay)
                            continue
                    except ValueError:
                        # 不是JSON，可能是二进制数据
                        pass

                # 获取媒体文件的内容类型
                content_type = response.headers.get('Content-Type', 'application/octet-stream')

                # 返回媒体文件数据和内容类型
                logger.info(f"Successfully retrieved media file with ID: {media_id}, content type: {content_type}")
                return response.content, content_type

            except requests.RequestException as e:
                logger.error(f"Network error when getting media file: {e}")

                # 如果是最后一次重试，则抛出异常
                if retry == self.retry_count - 1:
                    raise ValueError(f"Failed to get media file after {self.retry_count} attempts: {e}")

                # 否则等待后重试
                time.sleep(self.retry_delay)

        # 如果所有重试都失败，抛出异常
        raise ValueError(f"Failed to get media file after {self.retry_count} attempts")

    def send_markdown_message(self, to_user, content, agent_id):
        """
        发送markdown消息

        Args:
            to_user (str): 接收消息的用户ID，多个用户用'|'分隔
            content (str): markdown格式的消息内容
            agent_id (int/str): 企业应用ID

        Returns:
            dict: 企业微信API的响应数据

        Raises:
            ValueError: 发送消息失败时抛出
        """
        url = f"{self.base_url}/message/send?access_token={self.get_access_token()}"

        # 确保agent_id是整数
        try:
            agent_id = int(agent_id)
        except (ValueError, TypeError):
            logger.error(f"Invalid agent_id: {agent_id}, must be an integer")
            raise ValueError(f"Invalid agent_id: {agent_id}, must be an integer")

        payload = {
            "touser": to_user,
            "msgtype": "markdown",
            "agentid": agent_id,
            "markdown": {"content": content}
        }

        for retry in range(self.retry_count):
            try:
                logger.debug(f"Sending markdown message to {to_user}, attempt {retry+1}/{self.retry_count}")
                response = requests.post(url, json=payload, timeout=5)
                response.raise_for_status()
                data = response.json()

                if data.get('errcode') == 0:
                    logger.info(f"Successfully sent markdown message to {to_user}")
                    return data
                elif data.get('errcode') == 40014:  # invalid access_token
                    logger.warning("Access token expired, refreshing...")
                    self.access_token = None  # 强制刷新token
                    url = f"{self.base_url}/message/send?access_token={self.get_access_token()}"
                    # 继续下一次重试
                else:
                    error_msg = f"Failed to send markdown message: {data.get('errmsg', 'Unknown error')}"
                    logger.error(error_msg)

                    # 如果是最后一次重试，则抛出异常
                    if retry == self.retry_count - 1:
                        raise ValueError(error_msg)

                    # 否则等待后重试
                    time.sleep(self.retry_delay)

            except requests.RequestException as e:
                logger.error(f"Network error when sending markdown message: {e}")

                # 如果是最后一次重试，则抛出异常
                if retry == self.retry_count - 1:
                    raise ValueError(f"Failed to send markdown message after {self.retry_count} attempts: {e}")

                # 否则等待后重试
                time.sleep(self.retry_delay)

        # 如果所有重试都失败，抛出异常
        raise ValueError(f"Failed to send markdown message after {self.retry_count} attempts")


# 单元测试代码
if __name__ == "__main__":
    import unittest
    from unittest.mock import patch, MagicMock

    class WeComClientTest(unittest.TestCase):
        @patch('config.get_config')
        def setUp(self, mock_get_config):
            # 模拟配置
            mock_get_config.return_value = {
                'wecom': {
                    'CorpID': 'test_corp_id',
                    'Secret': 'test_secret'
                }
            }
            self.client = WeComClient()

        @patch('requests.get')
        def test_get_access_token_success(self, mock_get):
            # 模拟成功响应
            mock_response = MagicMock()
            mock_response.json.return_value = {
                'errcode': 0,
                'access_token': 'test_token',
                'expires_in': 7200
            }
            mock_get.return_value = mock_response

            # 测试获取token
            token = self.client.get_access_token()
            self.assertEqual(token, 'test_token')
            self.assertTrue(self.client.token_expiry > time.time())

        @patch('requests.get')
        def test_get_access_token_failure(self, mock_get):
            # 模拟失败响应
            mock_response = MagicMock()
            mock_response.json.return_value = {
                'errcode': 40013,
                'errmsg': 'invalid appid'
            }
            mock_get.return_value = mock_response

            # 测试获取token失败
            with self.assertRaises(ValueError):
                self.client.get_access_token()

        @patch('requests.post')
        @patch.object(WeComClient, 'get_access_token')
        def test_send_text_message_success(self, mock_get_token, mock_post):
            # 模拟token和成功响应
            mock_get_token.return_value = 'test_token'
            mock_response = MagicMock()
            mock_response.json.return_value = {
                'errcode': 0,
                'errmsg': 'ok'
            }
            mock_post.return_value = mock_response

            # 测试发送消息
            result = self.client.send_text_message('test_user', 'Hello, world!', 1)
            self.assertEqual(result['errcode'], 0)

            # 验证请求参数
            args, kwargs = mock_post.call_args
            self.assertEqual(kwargs['json']['touser'], 'test_user')
            self.assertEqual(kwargs['json']['text']['content'], 'Hello, world!')
            self.assertEqual(kwargs['json']['agentid'], 1)

        @patch('requests.get')
        @patch.object(WeComClient, 'get_access_token')
        def test_get_user_info_success(self, mock_get_token, mock_get):
            # 模拟token和成功响应
            mock_get_token.return_value = 'test_token'
            mock_response = MagicMock()
            mock_response.json.return_value = {
                'errcode': 0,
                'userid': 'test_user',
                'name': 'Test User',
                'department': [1, 2],
                'position': 'Developer'
            }
            mock_get.return_value = mock_response

            # 测试获取用户信息
            result = self.client.get_user_info('test_user')
            self.assertEqual(result['errcode'], 0)
            self.assertEqual(result['name'], 'Test User')

        @patch('requests.post')
        @patch.object(WeComClient, 'get_access_token')
        def test_send_markdown_message_success(self, mock_get_token, mock_post):
            # 模拟token和成功响应
            mock_get_token.return_value = 'test_token'
            mock_response = MagicMock()
            mock_response.json.return_value = {
                'errcode': 0,
                'errmsg': 'ok'
            }
            mock_post.return_value = mock_response

            # 测试发送markdown消息
            markdown_content = "# 标题\n- 列表项1\n- 列表项2"
            result = self.client.send_markdown_message('test_user', markdown_content, 1)
            self.assertEqual(result['errcode'], 0)

            # 验证请求参数
            args, kwargs = mock_post.call_args
            self.assertEqual(kwargs['json']['touser'], 'test_user')
            self.assertEqual(kwargs['json']['markdown']['content'], markdown_content)
            self.assertEqual(kwargs['json']['agentid'], 1)

    # 运行单元测试
    unittest.main()