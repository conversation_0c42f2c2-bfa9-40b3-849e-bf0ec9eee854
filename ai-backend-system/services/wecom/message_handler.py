"""
消息解析和分发。解析解密后的消息（XML 格式），提取 MsgType、Content 等，分发到 agents/ 模块处理
此文件解析解密后的消息，调用 ../agents/customer_service_team/teaching_assistant_agent.py(暂未实现，此部分调用暂时pass) 模块处理用户意图，并通过 client.py 发送智能客服回复。
"""

import xml.etree.ElementTree as ET
import time
import random
import base64
import logging
import traceback
from config import get_config
from services.wecom.crypto import aes_encrypt, generate_signature
from services.wecom.client import WeComClient

# 配置日志
logger = logging.getLogger(__name__)

# 消息类型处理器映射
MESSAGE_HANDLERS = {}

def register_handler(msg_type):
    """
    消息处理器装饰器，用于注册不同类型消息的处理函数

    Args:
        msg_type (str): 消息类型，如'text', 'event'等

    Returns:
        function: 装饰器函数
    """
    def decorator(func):
        MESSAGE_HANDLERS[msg_type] = func
        return func
    return decorator

def extract_message_info(xml_content):
    """
    从XML中提取消息基本信息

    Args:
        xml_content (str): XML格式的消息内容

    Returns:
        tuple: (msg_type, from_user, to_user, create_time, msg_id)

    Raises:
        ValueError: 解析XML失败时抛出
    """
    try:
        root = ET.fromstring(xml_content)
        msg_type = root.find('MsgType').text
        from_user = root.find('FromUserName').text
        to_user = root.find('ToUserName').text
        create_time = root.find('CreateTime').text
        msg_id = root.find('MsgId').text if root.find('MsgId') is not None else None

        return root, msg_type, from_user, to_user, create_time, msg_id
    except Exception as e:
        logger.error(f"Failed to extract message info: {e}")
        logger.error(f"XML content: {xml_content}")
        raise ValueError(f"Failed to extract message info: {e}")

def handle_message(decrypted_xml):
    """
    处理企业微信消息的主函数

    Args:
        decrypted_xml (str): 解密后的XML消息内容

    Returns:
        str: 处理结果，通常为'success'

    Raises:
        ValueError: 处理消息失败时抛出
    """
    try:
        # 从配置中获取企业微信配置
        wecom_config = get_config()['wecom']
        token = wecom_config['Token']
        encoding_aes_key = wecom_config['EncodingAESKey']
        corp_id = wecom_config['CorpID']
        agent_id = wecom_config['AgentID']

        # 提取消息基本信息
        root, msg_type, from_user, to_user, create_time, msg_id = extract_message_info(decrypted_xml)

        # 记录接收到的消息
        logger.info(f"Received message: type={msg_type}, from={from_user}, to={to_user}, time={create_time}, id={msg_id}")

        # 根据消息类型分发处理
        if msg_type in MESSAGE_HANDLERS:
            logger.debug(f"Dispatching to handler for message type: {msg_type}")
            return MESSAGE_HANDLERS[msg_type](root, from_user, to_user, agent_id)
        else:
            logger.warning(f"No handler for message type: {msg_type}")
            return 'success'

    except Exception as e:
        logger.error(f"Message handling failed: {e}")
        logger.error(traceback.format_exc())
        # 即使处理失败，也返回success，避免企业微信重试
        return 'success'

@register_handler('text')
def handle_text_message(root, from_user, to_user, agent_id):
    """
    处理文本消息

    Args:
        root (ET.Element): XML根元素
        from_user (str): 发送者ID
        to_user (str): 接收者ID
        agent_id (str): 应用ID

    Returns:
        str: 处理结果
    """
    try:
        # 提取文本内容
        content = root.find('Content').text
        logger.info(f"Received text message from {from_user}: {content}")

        # 导入教学助手代理
        from agents.customer_service_team.teaching_assistant_agent import TeachingAssistantAgent
        from autogen_core import CancellationToken
        import asyncio

        # 创建教学助手代理实例
        teaching_assistant = TeachingAssistantAgent()

        # 创建取消令牌
        cancellation_token = CancellationToken()

        # 处理用户消息并获取回复
        logger.info(f"Calling teaching assistant agent to handle message from {from_user}")
        response_content = asyncio.run(teaching_assistant.handle_user_message(
            user_id=from_user,
            content=content,
            cancellation_token=cancellation_token
        ))

        logger.info(f"Teaching assistant response: {response_content[:100]}...")

        # 通过客户端发送回复
        client = WeComClient()
        client.send_text_message(from_user, response_content, agent_id)

        # 直接返回success，通过API发送消息
        return 'success'
    except Exception as e:
        logger.error(f"Failed to handle text message: {e}")
        logger.error(traceback.format_exc())

        # 发送错误回复
        try:
            error_message = "抱歉，处理您的消息时出现了错误，请稍后再试。"
            client = WeComClient()
            client.send_text_message(from_user, error_message, agent_id)
        except Exception as send_error:
            logger.error(f"Failed to send error message: {send_error}")

        return 'success'

@register_handler('event')
def handle_event_message(root, from_user, to_user, agent_id):
    """
    处理事件消息

    Args:
        root (ET.Element): XML根元素
        from_user (str): 发送者ID
        to_user (str): 接收者ID
        agent_id (str): 应用ID

    Returns:
        str: 处理结果
    """
    try:
        # 提取事件类型
        event_type = root.find('Event').text
        logger.info(f"Received event: {event_type} from {from_user}")

        # 根据事件类型处理
        if event_type == 'subscribe':
            # 处理订阅事件
            welcome_message = "感谢关注！我是您的智能助手，有什么可以帮您的吗？"
            client = WeComClient()
            client.send_text_message(from_user, welcome_message, agent_id)
        elif event_type == 'unsubscribe':
            # 处理取消订阅事件
            logger.info(f"User {from_user} unsubscribed")
        elif event_type == 'click':
            # 处理菜单点击事件
            key = root.find('EventKey').text
            logger.info(f"User {from_user} clicked menu item: {key}")

            # 根据菜单项发送不同回复
            response_content = f"您点击了菜单: {key}"
            client = WeComClient()
            client.send_text_message(from_user, response_content, agent_id)

        return 'success'
    except Exception as e:
        logger.error(f"Failed to handle event message: {e}")
        logger.error(traceback.format_exc())
        return 'success'

@register_handler('image')
def handle_image_message(root, from_user, to_user, agent_id):
    """
    处理图片消息

    Args:
        root (ET.Element): XML根元素
        from_user (str): 发送者ID
        to_user (str): 接收者ID
        agent_id (str): 应用ID

    Returns:
        str: 处理结果
    """
    try:
        # 提取图片信息
        pic_url = root.find('PicUrl').text
        media_id = root.find('MediaId').text
        logger.info(f"Received image from {from_user}, MediaId: {media_id}, PicUrl: {pic_url}")

        # 导入教学助手代理
        from agents.customer_service_team.teaching_assistant_agent import TeachingAssistantAgent
        from autogen_core import CancellationToken
        import asyncio

        # 创建教学助手代理实例
        teaching_assistant = TeachingAssistantAgent()

        # 创建取消令牌
        cancellation_token = CancellationToken()

        # 获取图片内容
        client = WeComClient()
        try:
            # 获取图片数据
            image_data, image_type = client.get_media(media_id)
            logger.info(f"Successfully retrieved image data, type: {image_type}, size: {len(image_data)} bytes")

            # 处理用户消息并获取回复
            logger.info(f"Calling teaching assistant agent to handle image from {from_user}")
            response_content = asyncio.run(teaching_assistant.handle_user_message(
                user_id=from_user,
                content="",  # 没有文本内容
                image_data=image_data,
                image_type=image_type,
                cancellation_token=cancellation_token
            ))
        except Exception as media_error:
            logger.error(f"Failed to retrieve or process image: {media_error}")
            logger.error(traceback.format_exc())

            # 如果无法获取图片，尝试使用pic_url
            if pic_url:
                logger.info(f"Trying to use PicUrl instead: {pic_url}")
                response_content = asyncio.run(teaching_assistant.handle_user_message(
                    user_id=from_user,
                    content="[用户发送了一张图片]",
                    cancellation_token=cancellation_token
                ))
            else:
                # 如果都失败了，发送一个更友好的通用回复
                response_content = "我看到您发送了一张图片，但我暂时无法查看图片内容。请您简要描述一下图片中的内容，比如这是一道什么题目，或者您想问什么问题，这样我才能更好地帮助您。"

        logger.info(f"Teaching assistant response: {response_content[:100]}...")

        # 通过客户端发送回复
        client.send_text_message(from_user, response_content, agent_id)

        return 'success'
    except Exception as e:
        logger.error(f"Failed to handle image message: {e}")
        logger.error(traceback.format_exc())

        # 发送错误回复
        try:
            error_message = "我看到您发送了一张图片，但在处理过程中遇到了一些问题。请您简要描述一下图片中的内容，或者直接输入您的问题，这样我就能更好地帮助您了。"
            client = WeComClient()
            client.send_text_message(from_user, error_message, agent_id)
        except Exception as send_error:
            logger.error(f"Failed to send error message: {send_error}")

        return 'success'

@register_handler('voice')
def handle_voice_message(root, from_user, to_user, agent_id):
    """
    处理语音消息

    Args:
        root (ET.Element): XML根元素
        from_user (str): 发送者ID
        to_user (str): 接收者ID
        agent_id (str): 应用ID

    Returns:
        str: 处理结果
    """
    try:
        # 提取语音信息
        media_id = root.find('MediaId').text
        format_type = root.find('Format').text
        logger.info(f"Received voice from {from_user}, MediaId: {media_id}, Format: {format_type}")

        # 发送回复
        response_content = "已收到您发送的语音，我们会尽快处理。"
        client = WeComClient()
        client.send_text_message(from_user, response_content, agent_id)

        return 'success'
    except Exception as e:
        logger.error(f"Failed to handle voice message: {e}")
        logger.error(traceback.format_exc())
        return 'success'

def generate_encrypted_reply(to_user, from_user, content, token, encoding_aes_key, corp_id):
    """
    生成加密的回复消息（用于直接在HTTP响应中返回消息，而不是通过API发送）

    Args:
        to_user (str): 接收者ID
        from_user (str): 发送者ID
        content (str): 消息内容
        token (str): 企业微信配置的Token
        encoding_aes_key (str): 企业微信配置的EncodingAESKey
        corp_id (str): 企业微信的企业ID

    Returns:
        str: 加密的XML回复
    """
    try:
        timestamp = str(int(time.time()))
        nonce = str(random.randint(100000, 999999))

        # 构造回复消息
        reply_msg = f"""<xml>
            <ToUserName><![CDATA[{to_user}]]></ToUserName>
            <FromUserName><![CDATA[{from_user}]]></FromUserName>
            <CreateTime>{timestamp}</CreateTime>
            <MsgType><![CDATA[text]]></MsgType>
            <Content><![CDATA[{content}]]></Content>
        </xml>"""

        # 加密消息
        key = base64.b64decode(encoding_aes_key + '=')
        encrypted_msg = aes_encrypt(reply_msg, key, corp_id)

        # 生成签名
        signature = generate_signature(timestamp, nonce, encrypted_msg, token)

        # 构造加密的XML回复
        reply_xml = f"""<xml>
            <Encrypt><![CDATA[{encrypted_msg}]]></Encrypt>
            <MsgSignature><![CDATA[{signature}]]></MsgSignature>
            <TimeStamp>{timestamp}</TimeStamp>
            <Nonce><![CDATA[{nonce}]]></Nonce>
        </xml>"""

        return reply_xml
    except Exception as e:
        logger.error(f"Failed to generate encrypted reply: {e}")
        logger.error(traceback.format_exc())
        raise ValueError(f"Failed to generate encrypted reply: {e}")


# 单元测试代码
if __name__ == "__main__":
    import unittest
    from unittest.mock import patch, MagicMock

    class MessageHandlerTest(unittest.TestCase):
        @patch('config.get_config')
        def setUp(self, mock_get_config):
            # 模拟配置
            mock_get_config.return_value = {
                'wecom': {
                    'Token': 'test_token',
                    'EncodingAESKey': 'jWmYm7qr5nMoAUwZRjGtBxmz3KA1tkAj3ykkR6q2B2C',
                    'CorpID': 'wx5823bf96d3bd56c7',
                    'AgentID': '1000002'
                }
            }

            # 设置日志
            logging.basicConfig(level=logging.DEBUG)

        def test_extract_message_info(self):
            # 测试提取消息信息
            xml_content = """<xml>
                <ToUserName><![CDATA[toUser]]></ToUserName>
                <FromUserName><![CDATA[fromUser]]></FromUserName>
                <CreateTime>1348831860</CreateTime>
                <MsgType><![CDATA[text]]></MsgType>
                <Content><![CDATA[this is a test]]></Content>
                <MsgId>1234567890123456</MsgId>
            </xml>"""

            root, msg_type, from_user, to_user, create_time, msg_id = extract_message_info(xml_content)

            self.assertEqual(msg_type, 'text')
            self.assertEqual(from_user, 'fromUser')
            self.assertEqual(to_user, 'toUser')
            self.assertEqual(create_time, '1348831860')
            self.assertEqual(msg_id, '1234567890123456')

        @patch.object(WeComClient, 'send_text_message')
        def test_handle_text_message(self, mock_send):
            # 测试处理文本消息
            xml_content = """<xml>
                <ToUserName><![CDATA[toUser]]></ToUserName>
                <FromUserName><![CDATA[fromUser]]></FromUserName>
                <CreateTime>1348831860</CreateTime>
                <MsgType><![CDATA[text]]></MsgType>
                <Content><![CDATA[this is a test]]></Content>
                <MsgId>1234567890123456</MsgId>
            </xml>"""

            result = handle_message(xml_content)
            self.assertEqual(result, 'success')

            # 验证发送消息被调用
            mock_send.assert_called_once()
            args, kwargs = mock_send.call_args
            self.assertEqual(args[0], 'fromUser')
            self.assertIn('this is a test', args[1])

        @patch.object(WeComClient, 'send_text_message')
        def test_handle_event_message(self, mock_send):
            # 测试处理事件消息
            xml_content = """<xml>
                <ToUserName><![CDATA[toUser]]></ToUserName>
                <FromUserName><![CDATA[fromUser]]></FromUserName>
                <CreateTime>1348831860</CreateTime>
                <MsgType><![CDATA[event]]></MsgType>
                <Event><![CDATA[subscribe]]></Event>
            </xml>"""

            result = handle_message(xml_content)
            self.assertEqual(result, 'success')

            # 验证发送消息被调用
            mock_send.assert_called_once()
            args, kwargs = mock_send.call_args
            self.assertEqual(args[0], 'fromUser')
            self.assertIn('感谢关注', args[1])

        def test_generate_encrypted_reply(self):
            # 测试生成加密回复
            to_user = 'fromUser'
            from_user = 'toUser'
            content = 'test reply'
            token = 'test_token'
            encoding_aes_key = 'jWmYm7qr5nMoAUwZRjGtBxmz3KA1tkAj3ykkR6q2B2C'
            corp_id = 'wx5823bf96d3bd56c7'

            reply_xml = generate_encrypted_reply(to_user, from_user, content, token, encoding_aes_key, corp_id)

            # 验证回复包含必要的元素
            self.assertIn('<Encrypt>', reply_xml)
            self.assertIn('<MsgSignature>', reply_xml)
            self.assertIn('<TimeStamp>', reply_xml)
            self.assertIn('<Nonce>', reply_xml)

    # 运行单元测试
    unittest.main()