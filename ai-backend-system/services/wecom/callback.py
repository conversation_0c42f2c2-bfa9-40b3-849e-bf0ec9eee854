"""
此文件是回调服务的入口，处理企业微信的 GET（URL 验证）和 POST（消息/事件）请求，调用 crypto.py 验证签名和解密，调用 message_handler.py 处理消息。
"""

from flask import Flask, request, abort, Response
import logging
import base64
import time
import xml.etree.ElementTree as ET
import traceback
from config import get_config
from services.wecom.crypto import aes_decrypt, verify_signature
from services.wecom.message_handler import handle_message

# 创建Flask应用
app = Flask(__name__)

# 配置日志
logger = logging.getLogger(__name__)

# 加载配置
try:
    wecom_config = get_config()['wecom']
    TOKEN = wecom_config['Token']
    ENCODING_AES_KEY = wecom_config['EncodingAESKey']
    CORP_ID = wecom_config['CorpID']
    logger.info("WeChat Work callback configuration loaded successfully")
except Exception as e:
    logger.error(f"Failed to load WeChat Work configuration: {e}")
    logger.error(traceback.format_exc())
    # 设置默认值，避免程序崩溃
    TOKEN = "YOUR_TOKEN"
    ENCODING_AES_KEY = "YOUR_ENCODING_AES_KEY"
    CORP_ID = "YOUR_CORP_ID"

# 用于防止重放攻击的 nonce 缓存
nonce_cache = set()
NONCE_CACHE_TTL = 300  # 5 分钟（单位：秒）

def clean_nonce_cache():
    """
    清理过期的nonce缓存

    移除超过TTL时间的nonce，防止缓存无限增长
    """
    current_time = int(time.time())
    expired = [nk for nk in nonce_cache if current_time - int(nk.split(':')[1]) > NONCE_CACHE_TTL]

    if expired:
        logger.debug(f"Cleaning {len(expired)} expired nonces from cache")
        for nk in expired:
            nonce_cache.discard(nk)

def validate_timestamp(timestamp):
    """
    验证时间戳是否有效

    Args:
        timestamp (str): 请求中的时间戳

    Returns:
        bool: 时间戳是否有效

    Raises:
        ValueError: 时间戳格式无效时抛出
    """
    try:
        timestamp_int = int(timestamp)
        current_time = int(time.time())
        time_diff = abs(current_time - timestamp_int)

        if time_diff > NONCE_CACHE_TTL:
            logger.warning(f"Timestamp expired: {timestamp}, diff: {time_diff}s")
            return False

        return True
    except ValueError:
        logger.warning(f"Invalid timestamp format: {timestamp}")
        raise ValueError("Invalid timestamp format")

@app.route('/wecom/callback', methods=['GET'])
def verify_url():
    """
    处理GET请求（URL验证）

    企业微信在配置回调URL时会发送GET请求进行验证

    Returns:
        str: 解密后的echostr

    Raises:
        400: 缺少必要参数
        403: 签名验证失败或时间戳过期
        500: 解密失败
    """
    # 获取请求参数
    signature = request.args.get('msg_signature', '')
    timestamp = request.args.get('timestamp', '')
    nonce = request.args.get('nonce', '')
    echostr = request.args.get('echostr', '')

    # 记录请求信息
    logger.info(f"Received URL verification request: signature={signature}, timestamp={timestamp}, nonce={nonce}")

    # 验证必要参数
    if not all([signature, timestamp, nonce, echostr]):
        logger.warning("Missing required parameters")
        abort(400, "Missing parameters")

    # 验证签名
    if not verify_signature(signature, timestamp, nonce, echostr, TOKEN):
        logger.warning(f"Invalid signature: {signature}")
        abort(403, "Invalid signature")

    # 验证时间戳
    try:
        if not validate_timestamp(timestamp):
            abort(403, "Timestamp expired")
    except ValueError:
        abort(400, "Invalid timestamp format")

    # 解密echostr
    try:
        logger.debug("Decrypting echostr")
        key = base64.b64decode(ENCODING_AES_KEY + '=')
        encrypted_text = base64.b64decode(echostr)
        decrypted_text = aes_decrypt(encrypted_text, key, CORP_ID)

        # 记录nonce防止重放攻击
        nonce_key = f"{nonce}:{timestamp}"
        nonce_cache.add(nonce_key)
        clean_nonce_cache()

        logger.info("URL verification successful")
        return decrypted_text
    except Exception as e:
        logger.error(f"Failed to decrypt echostr: {e}")
        logger.error(traceback.format_exc())
        abort(500, "Decryption failed")

@app.route('/WW_verify_wKayc7FPhsFkcaIK.txt', methods=['GET'])
def wecom_domain_verification():
    """
    Handles the WeCom domain verification request.
    """
    return Response("wKayc7FPhsFkcaIK", mimetype="text/plain")

@app.route('/wecom/callback', methods=['POST'])
def receive_message():
    """
    处理POST请求（接收消息或事件）

    企业微信在用户发送消息或触发事件时会发送POST请求

    Returns:
        str: 处理结果，通常为'success'

    Raises:
        400: 缺少必要参数或XML格式错误
        403: 签名验证失败、时间戳过期或检测到重放攻击
        500: 处理失败
    """
    # 获取请求参数
    signature = request.args.get('msg_signature', '')
    timestamp = request.args.get('timestamp', '')
    nonce = request.args.get('nonce', '')

    # 记录请求信息
    logger.info(f"Received message: signature={signature}, timestamp={timestamp}, nonce={nonce}")

    # 验证必要参数
    if not all([signature, timestamp, nonce]):
        logger.warning("Missing required parameters")
        abort(400, "Missing parameters")

    # 验证时间戳并检查重放攻击
    try:
        if not validate_timestamp(timestamp):
            abort(403, "Timestamp expired")

        nonce_key = f"{nonce}:{timestamp}"
        if nonce_key in nonce_cache:
            logger.warning(f"Potential replay attack detected: {nonce_key}")
            abort(403, "Nonce reused")
    except ValueError:
        abort(400, "Invalid timestamp format")

    # 解析XML请求体
    try:
        xml_data = request.data.decode('utf-8')
        logger.debug(f"Received XML: {xml_data}")

        root = ET.fromstring(xml_data)
        encrypted_msg = root.find('Encrypt')

        if encrypted_msg is None or not encrypted_msg.text:
            logger.warning("Missing Encrypt field in XML")
            abort(400, "Invalid XML: missing Encrypt field")

        encrypted_msg = encrypted_msg.text
    except Exception as e:
        logger.error(f"Failed to parse XML: {e}")
        logger.error(traceback.format_exc())
        abort(400, "Invalid XML format")

    # 验证签名
    if not verify_signature(signature, timestamp, nonce, encrypted_msg, TOKEN):
        logger.warning(f"Invalid signature: {signature}")
        abort(403, "Invalid signature")

    # 解密消息并处理
    try:
        logger.debug("Decrypting message")
        key = base64.b64decode(ENCODING_AES_KEY + '=')
        encrypted_text = base64.b64decode(encrypted_msg)
        decrypted_xml = aes_decrypt(encrypted_text, key, CORP_ID)

        # 记录nonce防止重放攻击
        nonce_cache.add(nonce_key)
        clean_nonce_cache()

        # 分发到消息处理器
        logger.debug("Dispatching to message handler")
        response = handle_message(decrypted_xml)

        logger.info("Message processed successfully")
        return response or 'success'
    except Exception as e:
        logger.error(f"Failed to process message: {e}")
        logger.error(traceback.format_exc())
        abort(500, "Processing failed")

def create_app(test_config=None):
    """
    创建Flask应用的工厂函数

    用于在测试环境中创建应用实例

    Args:
        test_config (dict, optional): 测试配置

    Returns:
        Flask: Flask应用实例
    """
    if test_config:
        app.config.update(test_config)
    return app

# 启动服务器（仅在直接运行此文件时）
if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        filename='ai-backend-system/logs/wecom_callback.log',
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    logger.info("Starting WeChat Work callback server...")

    try:
        # 在生产环境中应该使用WSGI服务器（如gunicorn）和HTTPS
        app.run(host='0.0.0.0', port=5000, ssl_context='adhoc')
        logger.info("Server started on port 5000 with HTTPS")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        logger.error(traceback.format_exc())


# 单元测试代码
if __name__ == "__test__":  # 使用特殊名称避免与主程序入口冲突
    import unittest
    from unittest.mock import patch, MagicMock
    import json

    class CallbackTest(unittest.TestCase):
        def setUp(self):
            self.app = create_app({'TESTING': True})
            self.client = self.app.test_client()

            # 清空nonce缓存
            nonce_cache.clear()

            # 测试数据
            self.timestamp = str(int(time.time()))
            self.nonce = '1234567890'
            self.token = TOKEN
            self.encoding_aes_key = ENCODING_AES_KEY
            self.corp_id = CORP_ID

        @patch('ai-backend-system.services.wecom.crypto.verify_signature')
        @patch('ai-backend-system.services.wecom.crypto.aes_decrypt')
        def test_verify_url(self, mock_decrypt, mock_verify):
            # 模拟签名验证和解密
            mock_verify.return_value = True
            mock_decrypt.return_value = 'test_echo_str'

            # 发送GET请求
            response = self.client.get(
                '/wecom/callback',
                query_string={
                    'msg_signature': 'test_signature',
                    'timestamp': self.timestamp,
                    'nonce': self.nonce,
                    'echostr': 'test_encrypted_str'
                }
            )

            # 验证响应
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.data.decode('utf-8'), 'test_echo_str')

            # 验证nonce缓存
            self.assertIn(f"{self.nonce}:{self.timestamp}", nonce_cache)

        @patch('ai-backend-system.services.wecom.crypto.verify_signature')
        def test_verify_url_invalid_signature(self, mock_verify):
            # 模拟签名验证失败
            mock_verify.return_value = False

            # 发送GET请求
            response = self.client.get(
                '/wecom/callback',
                query_string={
                    'msg_signature': 'invalid_signature',
                    'timestamp': self.timestamp,
                    'nonce': self.nonce,
                    'echostr': 'test_encrypted_str'
                }
            )

            # 验证响应
            self.assertEqual(response.status_code, 403)

        @patch('ai-backend-system.services.wecom.crypto.verify_signature')
        @patch('ai-backend-system.services.wecom.crypto.aes_decrypt')
        @patch('ai-backend-system.services.wecom.message_handler.handle_message')
        def test_receive_message(self, mock_handle, mock_decrypt, mock_verify):
            # 模拟签名验证、解密和消息处理
            mock_verify.return_value = True
            mock_decrypt.return_value = '<xml><MsgType>text</MsgType></xml>'
            mock_handle.return_value = 'success'

            # 构造XML请求体
            xml_body = '<xml><Encrypt>test_encrypted_msg</Encrypt></xml>'

            # 发送POST请求
            response = self.client.post(
                '/wecom/callback',
                query_string={
                    'msg_signature': 'test_signature',
                    'timestamp': self.timestamp,
                    'nonce': self.nonce
                },
                data=xml_body,
                content_type='text/xml'
            )

            # 验证响应
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.data.decode('utf-8'), 'success')

            # 验证消息处理被调用
            mock_handle.assert_called_once_with('<xml><MsgType>text</MsgType></xml>')

        def test_replay_attack_detection(self):
            # 添加nonce到缓存
            nonce_key = f"{self.nonce}:{self.timestamp}"
            nonce_cache.add(nonce_key)

            # 构造XML请求体
            xml_body = '<xml><Encrypt>test_encrypted_msg</Encrypt></xml>'

            # 发送POST请求（使用相同的nonce和timestamp）
            response = self.client.post(
                '/wecom/callback',
                query_string={
                    'msg_signature': 'test_signature',
                    'timestamp': self.timestamp,
                    'nonce': self.nonce
                },
                data=xml_body,
                content_type='text/xml'
            )

            # 验证响应（应该检测到重放攻击）
            self.assertEqual(response.status_code, 403)

        def test_missing_parameters(self):
            # 发送缺少参数的GET请求
            response = self.client.get('/wecom/callback')
            self.assertEqual(response.status_code, 400)

            # 发送缺少参数的POST请求
            response = self.client.post('/wecom/callback')
            self.assertEqual(response.status_code, 400)

        def test_invalid_xml(self):
            # 发送无效的XML
            response = self.client.post(
                '/wecom/callback',
                query_string={
                    'msg_signature': 'test_signature',
                    'timestamp': self.timestamp,
                    'nonce': self.nonce
                },
                data='invalid xml',
                content_type='text/xml'
            )
            self.assertEqual(response.status_code, 400)

            # 发送缺少Encrypt字段的XML
            response = self.client.post(
                '/wecom/callback',
                query_string={
                    'msg_signature': 'test_signature',
                    'timestamp': self.timestamp,
                    'nonce': self.nonce
                },
                data='<xml><NotEncrypt>test</NotEncrypt></xml>',
                content_type='text/xml'
            )
            self.assertEqual(response.status_code, 400)

    # 运行单元测试
    unittest.main()