"""
封装 AES 加密解密和签名验证逻辑，用于处理企业微信消息的加解密和签名验证
此文件封装 AES 加密解密和签名验证逻辑，供 callback.py 和 message_handler.py 使用。
"""

import hashlib
import base64
import struct
import os
import logging
from Crypto.Cipher import AES

# 配置日志
logger = logging.getLogger(__name__)

def aes_decrypt(encrypted_text, key, corp_id):
    """
    解密企业微信消息

    Args:
        encrypted_text (bytes): 加密的消息内容
        key (bytes): 解密密钥
        corp_id (str): 企业微信的企业ID

    Returns:
        str: 解密后的消息内容

    Raises:
        ValueError: 解密失败或CorpID验证失败时抛出
    """
    try:
        # 使用AES-CBC模式解密
        cipher = AES.new(key, AES.MODE_CBC, key[:16])
        decrypted = cipher.decrypt(encrypted_text)

        # 移除PKCS#7填充
        pad = decrypted[-1]
        if pad < 1 or pad > 32:
            raise ValueError("Invalid padding")
        decrypted = decrypted[:-pad]

        # 解析消息长度
        msg_len = struct.unpack('>I', decrypted[16:20])[0]
        if msg_len < 0 or msg_len > len(decrypted) - 20:
            raise ValueError("Invalid message length")

        # 提取消息内容
        msg = decrypted[20:20 + msg_len]

        # 验证CorpID
        received_corp_id = decrypted[20 + msg_len:].decode('utf-8')
        if received_corp_id != corp_id:
            logger.error(f"Invalid CorpID: expected {corp_id}, got {received_corp_id}")
            raise ValueError("Invalid CorpID")

        return msg.decode('utf-8')
    except Exception as e:
        logger.error(f"Decryption error: {e}")
        raise ValueError(f"Decryption error: {e}")

def aes_encrypt(plain_text, key, corp_id):
    """
    加密消息（用于回复）

    Args:
        plain_text (str): 要加密的消息内容
        key (bytes): 加密密钥
        corp_id (str): 企业微信的企业ID

    Returns:
        str: Base64编码的加密消息

    Raises:
        ValueError: 加密失败时抛出
    """
    try:
        # 添加随机字符串和消息长度
        random_str = os.urandom(16)
        msg_len = struct.pack('>I', len(plain_text))
        msg = random_str + msg_len + plain_text.encode('utf-8') + corp_id.encode('utf-8')

        # 添加PKCS#7填充
        pad = 32 - (len(msg) % 32)
        msg += bytes([pad] * pad)

        # 使用AES-CBC模式加密
        cipher = AES.new(key, AES.MODE_CBC, key[:16])
        encrypted = cipher.encrypt(msg)

        # Base64编码
        return base64.b64encode(encrypted).decode('utf-8')
    except Exception as e:
        logger.error(f"Encryption error: {e}")
        raise ValueError(f"Encryption error: {e}")

def verify_signature(signature, timestamp, nonce, data, token):
    """
    验证请求签名

    Args:
        signature (str): 请求中的签名
        timestamp (str): 请求中的时间戳
        nonce (str): 请求中的随机数
        data (str): 请求中的数据（如echostr或加密消息）
        token (str): 企业微信配置的Token

    Returns:
        bool: 签名是否有效
    """
    try:
        # 按照字典序排序并拼接
        sign_str = ''.join(sorted([token, timestamp, nonce, data]))
        # 计算SHA1哈希
        sha1 = hashlib.sha1(sign_str.encode('utf-8')).hexdigest()
        # 比较签名
        is_valid = sha1 == signature
        if not is_valid:
            logger.warning(f"Signature verification failed: expected {signature}, got {sha1}")
        return is_valid
    except Exception as e:
        logger.error(f"Signature verification failed: {e}")
        return False

def generate_signature(timestamp, nonce, data, token):
    """
    生成签名（用于回复）

    Args:
        timestamp (str): 时间戳
        nonce (str): 随机数
        data (str): 要签名的数据（如加密消息）
        token (str): 企业微信配置的Token

    Returns:
        str: 生成的签名

    Raises:
        ValueError: 签名生成失败时抛出
    """
    try:
        # 按照字典序排序并拼接
        sign_str = ''.join(sorted([token, timestamp, nonce, data]))
        # 计算SHA1哈希
        return hashlib.sha1(sign_str.encode('utf-8')).hexdigest()
    except Exception as e:
        logger.error(f"Signature generation failed: {e}")
        raise ValueError(f"Signature generation failed: {e}")


# 单元测试代码
if __name__ == "__main__":
    import unittest

    class CryptoTest(unittest.TestCase):
        def setUp(self):
            # 测试数据
            self.token = "test_token"
            self.encoding_aes_key = "jWmYm7qr5nMoAUwZRjGtBxmz3KA1tkAj3ykkR6q2B2C"
            self.corp_id = "wx5823bf96d3bd56c7"
            self.key = base64.b64decode(self.encoding_aes_key + "=")

        def test_signature_verification(self):
            # 测试签名验证
            timestamp = "1409659813"
            nonce = "1372623149"
            data = "test_data"
            # 手动计算正确的签名
            sign_str = ''.join(sorted([self.token, timestamp, nonce, data]))
            expected_signature = hashlib.sha1(sign_str.encode('utf-8')).hexdigest()

            # 验证正确的签名
            self.assertTrue(verify_signature(expected_signature, timestamp, nonce, data, self.token))
            # 验证错误的签名
            self.assertFalse(verify_signature("wrong_signature", timestamp, nonce, data, self.token))

        def test_signature_generation(self):
            # 测试签名生成
            timestamp = "1409659813"
            nonce = "1372623149"
            data = "test_data"
            # 手动计算正确的签名
            sign_str = ''.join(sorted([self.token, timestamp, nonce, data]))
            expected_signature = hashlib.sha1(sign_str.encode('utf-8')).hexdigest()

            # 生成签名并验证
            generated_signature = generate_signature(timestamp, nonce, data, self.token)
            self.assertEqual(expected_signature, generated_signature)

        def test_encryption_decryption(self):
            # 测试加密和解密
            plain_text = "这是一条测试消息"

            # 加密
            encrypted = aes_encrypt(plain_text, self.key, self.corp_id)
            self.assertIsNotNone(encrypted)

            # 解密
            decrypted = aes_decrypt(base64.b64decode(encrypted), self.key, self.corp_id)
            self.assertEqual(plain_text, decrypted)

        def test_decryption_with_invalid_corp_id(self):
            # 测试使用错误的CorpID解密
            plain_text = "这是一条测试消息"

            # 加密
            encrypted = aes_encrypt(plain_text, self.key, self.corp_id)

            # 使用错误的CorpID解密
            with self.assertRaises(ValueError):
                aes_decrypt(base64.b64decode(encrypted), self.key, "wrong_corp_id")

    # 运行单元测试
    unittest.main()