# ai-backend-system/tests/__init__.py
"""
tests 包的初始化模块.

该模块通常为空，用于将 tests 目录标记为 Python 包，方便 pytest 框架自动识别和运行测试用例.
可以在该模块中进行一些全局的测试配置，例如设置测试环境、加载测试数据等 (如果需要).

包含的测试模块:
- test_agents.py: 测试 agents 模块中的 Agent 类
- test_rag_knowledge_base.py: 测试 RAG 知识库功能
- test_services.py: 测试各种服务功能
- test_utils.py: 测试 utils 目录下的工具类
- test_memory.py: 测试 utils.memory 目录下的记忆类 (HierarchicalMemory, KnowledgeGraphMemory, MultiUserMemory)
"""