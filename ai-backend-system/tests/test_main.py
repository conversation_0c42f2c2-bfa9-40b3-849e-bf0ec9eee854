#!/usr/bin/env python3
# ai-backend-system/tests/test_main.py
"""
测试 main.py 模块的功能
"""
import os
import sys
import unittest
from unittest.mock import patch, MagicMock

# 确保 ai-backend-system 目录在 Python 路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# 导入被测试的模块
import main


class TestMain(unittest.TestCase):
    """测试 main.py 模块的功能"""

    @patch('main.get_config')
    @patch('logging.basicConfig')
    def test_setup_logging(self, mock_basicConfig, mock_get_config):
        """测试日志系统初始化"""
        # 设置模拟返回值
        mock_config = {
            'logging': {
                'log_level': 'INFO',
                'log_format': '%(asctime)s - %(levelname)s - %(name)s - %(message)s',
                'log_file_path': 'logs/test.log'
            }
        }
        mock_get_config.return_value = mock_config

        # 调用函数
        logger = main.setup_logging()

        # 验证结果
        self.assertIsNotNone(logger)
        mock_basicConfig.assert_called_once()
        mock_get_config.assert_called_once()

    @patch('main.get_config')
    def test_check_environment_success(self, mock_get_config):
        """测试环境检查成功的情况"""
        # 设置模拟返回值
        mock_config = {
            'wecom': {
                'Token': 'test_token',
                'EncodingAESKey': 'test_encoding_aes_key',
                'CorpID': 'test_corp_id',
                'Secret': 'test_secret',
                'AgentID': 'test_agent_id'
            }
        }
        mock_get_config.return_value = mock_config
        mock_logger = MagicMock()

        # 调用函数
        result = main.check_environment(mock_logger)

        # 验证结果
        self.assertTrue(result)
        mock_logger.info.assert_called_with("环境变量和配置检查通过")

    @patch('main.get_config')
    def test_check_environment_missing_config(self, mock_get_config):
        """测试环境检查失败的情况 - 缺少配置项"""
        # 设置模拟返回值
        mock_config = {
            'wecom': {
                'Token': 'test_token',
                # 缺少 EncodingAESKey
                'CorpID': 'test_corp_id',
                'Secret': 'test_secret',
                'AgentID': 'test_agent_id'
            }
        }
        mock_get_config.return_value = mock_config
        mock_logger = MagicMock()

        # 调用函数
        result = main.check_environment(mock_logger)

        # 验证结果
        self.assertFalse(result)
        mock_logger.error.assert_called_with("缺少必要的企业微信配置: EncodingAESKey")

    @patch('main.get_config')
    def test_check_environment_empty_config(self, mock_get_config):
        """测试环境检查失败的情况 - 配置项为空"""
        # 设置模拟返回值
        mock_config = {
            'wecom': {
                'Token': 'test_token',
                'EncodingAESKey': '',  # 空值
                'CorpID': 'test_corp_id',
                'Secret': 'test_secret',
                'AgentID': 'test_agent_id'
            }
        }
        mock_get_config.return_value = mock_config
        mock_logger = MagicMock()

        # 调用函数
        result = main.check_environment(mock_logger)

        # 验证结果
        self.assertFalse(result)
        mock_logger.error.assert_called_with("企业微信配置 'EncodingAESKey' 的值无效或为空")

    @patch('socket.socket')
    def test_get_local_ip(self, mock_socket):
        """测试获取本机IP地址"""
        # 设置模拟返回值
        mock_socket_instance = MagicMock()
        mock_socket_instance.getsockname.return_value = ('*************', 12345)
        mock_socket.return_value = mock_socket_instance

        # 调用函数
        ip = main.get_local_ip()

        # 验证结果
        self.assertEqual(ip, '*************')
        mock_socket_instance.connect.assert_called_with(("*******", 80))
        mock_socket_instance.close.assert_called_once()

    @patch('socket.socket')
    def test_get_local_ip_exception(self, mock_socket):
        """测试获取本机IP地址异常情况"""
        # 设置模拟返回值
        mock_socket_instance = MagicMock()
        mock_socket_instance.connect.side_effect = Exception("Connection error")
        mock_socket.return_value = mock_socket_instance

        # 调用函数
        ip = main.get_local_ip()

        # 验证结果
        self.assertEqual(ip, '127.0.0.1')

    @patch('services.wecom.callback.app')
    def test_load_flask_app(self, mock_app):
        """测试加载Flask应用"""
        # 设置模拟返回值
        mock_logger = MagicMock()

        # 调用函数
        app = main.load_flask_app(mock_logger)

        # 验证结果
        self.assertEqual(app, mock_app)
        mock_logger.info.assert_called_with("Flask应用加载成功")


if __name__ == '__main__':
    unittest.main()
