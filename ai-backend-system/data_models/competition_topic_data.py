"""
定义竞赛题目数据模型 (CompetitionTopicData) 类。

该模块使用 Pydantic 库定义竞赛题目数据的结构和验证规则。
包含竞赛题目的完整信息，如题目描述、输入输出格式、样例、数据规模约定等。
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, ClassVar, Dict, Any, Tuple
from data_models.content_data_model import ContentDataModel
from data_models.content_type import ContentType
from datetime import datetime
import json
from utils.db.db_connector import DBConnector
import logging
import os

class InputOutputExample(BaseModel):
    """输入输出样例模型"""
    input_example: str = Field(..., description="输入样例")
    output_example: str = Field(..., description="输出样例")
    explanation: Optional[str] = Field(None, description="样例解释")

class DataScale(BaseModel):
    """数据规模约定模型"""
    test_point: str = Field(..., description="测试点编号")
    scale: str = Field(..., description="数据规模")

class CompetitionTopicData(ContentDataModel):
    """竞赛题目数据模型，包含竞赛题目的完整信息"""
    
    # 基本类型标识
    content_type: ClassVar[ContentType] = ContentType.COMPETITION_TOPIC
    
    # 竞赛题目基本信息
    title: Optional[str] = Field(None, description="题目标题")
    competition_name: Optional[str] = Field(None, description="所属竞赛名称")
    problem_id: Optional[str] = Field(None, description="题目编号")
    
    # 题目内容
    description: Optional[str] = Field(None, description="题目描述")
    input_format: Optional[str] = Field(None, description="输入格式")
    output_format: Optional[str] = Field(None, description="输出格式")
    
    # 样例
    examples: Optional[List[InputOutputExample]] = Field(None, description="输入输出样例列表")
    
    # 说明和提示
    notes: Optional[str] = Field(None, description="说明/提示")
    data_scale: Optional[List[DataScale]] = Field(None, description="数据规模与约定")
    hints: Optional[str] = Field(None, description="提示")
    
    # 时间信息
    created_at: Optional[datetime] = Field(default_factory=datetime.now, description="创建时间")
    updated_at: Optional[datetime] = Field(default_factory=datetime.now, description="更新时间")
    
    # 额外信息
    difficulty: Optional[str] = Field(None, description="难度级别")
    tags: Optional[List[str]] = Field(None, description="题目标签")
    metadata: Optional[Dict[str, Any]] = Field(None, description="其他元数据")
    
    def export_prompts(self) -> str:
        """导出竞赛题目数据模型的提示"""
        return """The competition topic data model includes the following fields:
        - title: The title of the problem
        - competition_name: The name of the competition
        - problem_id: The ID of the problem
        - description: Detailed description of the problem
        - input_format: The format of the input
        - output_format: The format of the output
        - examples: List of input/output examples with explanations
        - notes: explanations or notes about the problem, sometimes it will provide an image url, that's fine just crawl the url link by this situation.
        - data_scale: Information about the scale of test data and the conventions for the problem
        - hints: Hints for solving the problem
        - created_at: When the problem was created
        - updated_at: When the problem was last updated
        - difficulty: The difficulty level of the problem. Look for sections labeled "难度" or "等级" in the problem page. Common values include "入门", "普及-", "普及/提高-", "普及+/提高", "提高+/省选-", "省选/NOI-", "NOI/NOI+/CTSC" etc.
        - tags: Tags associated with the problem. Look for sections labeled "标签" or "算法标签" in the problem page. These are usually displayed as a list of algorithm categories like "动态规划 DP", "高精度", "区间 DP", "贪心", "搜索" etc.
        - metadata: Additional metadata about the problem
        
        Important: When crawling the page, pay special attention to finding the difficulty and tags information. These are typically displayed in a separate section of the problem page, often in a sidebar or in a metadata section near the problem title. They may be preceded by labels like "难度:" or "标签:".
        
        Critical Note on Selectors: When defining selectors in your schema, ensure that your baseSelector is broad enough to include ALL relevant page sections. For Luogu problems, use "div#app" as the baseSelector instead of more restrictive selectors like "div.main-container" which might exclude the sidebar containing difficulty and tags information. The difficulty is typically found in a div with class "l-flex-info-row" containing the text "难度", and tags are found as span elements with class "luogu-tag" in a section with heading "标签".
        """

    def to_db_format(self) -> Tuple[Dict[str, Any], List[Dict[str, Any]], List[Dict[str, Any]]]:
        """将模型转换为数据库存储格式
        
        Returns:
            Tuple: (主表数据, 样例数据列表, 数据规模列表)
        """
        # 主表数据
        main_data = {
            "title": self.title,
            "competition_name": self.competition_name,
            "problem_id": self.problem_id,
            "description": self.description,
            "input_format": self.input_format,
            "output_format": self.output_format,
            "notes": self.notes,
            "hints": self.hints,
            "difficulty": self.difficulty,
            "tags": self.tags,
            "metadata": json.dumps(self.metadata) if self.metadata else None
        }
        
        # 样例数据
        examples_data = []
        if self.examples:
            for example in self.examples:
                examples_data.append({
                    "input_example": example.input_example,
                    "output_example": example.output_example,
                    "explanation": example.explanation
                })
        
        # 数据规模
        data_scales = []
        if self.data_scale:
            for scale in self.data_scale:
                data_scales.append({
                    "test_point": scale.test_point,
                    "scale": scale.scale
                })
                
        return main_data, examples_data, data_scales
    
    def save_to_db(self, db_connector: DBConnector) -> int:
        """将竞赛题目数据保存到数据库
        
        Args:
            db_connector: 数据库连接器
            
        Returns:
            int: 插入的记录ID
        """
        # 转换为数据库格式
        main_data, examples_data, data_scales = self.to_db_format()
        
        # 处理标签和元数据的序列化
        if isinstance(main_data['tags'], list):
            main_data['tags'] = json.dumps(main_data['tags'])
        
        try:
            if db_connector._is_using_supabase():
                # Supabase 存储过程调用
                sql = "SELECT insert_competition_topic($1, $2, $3)"
                params = [
                    json.dumps(main_data),
                    json.dumps(examples_data),
                    json.dumps(data_scales)
                ]
                result = db_connector.execute_sql(sql, params, fetch_one=True)
                topic_id = result[0]
            else:
                # SQLite 事务处理
                conn = db_connector.sqlite_conn
                cursor = conn.cursor()
                
                # 开始事务
                conn.execute("BEGIN TRANSACTION")
                
                try:
                    # 插入主表数据
                    columns = ', '.join(main_data.keys())
                    placeholders = ', '.join(['?' for _ in main_data])
                    sql = f"INSERT INTO backend_competition_topics ({columns}) VALUES ({placeholders})"
                    cursor.execute(sql, list(main_data.values()))
                    topic_id = cursor.lastrowid
                    
                    # 插入样例数据
                    if examples_data:
                        for example in examples_data:
                            example['topic_id'] = topic_id
                            columns = ', '.join(example.keys())
                            placeholders = ', '.join(['?' for _ in example])
                            sql = f"INSERT INTO backend_competition_topic_examples ({columns}) VALUES ({placeholders})"
                            cursor.execute(sql, list(example.values()))
                    
                    # 插入数据规模
                    if data_scales:
                        for scale in data_scales:
                            scale['topic_id'] = topic_id
                            columns = ', '.join(scale.keys())
                            placeholders = ', '.join(['?' for _ in scale])
                            sql = f"INSERT INTO backend_competition_topic_data_scales ({columns}) VALUES ({placeholders})"
                            cursor.execute(sql, list(scale.values()))
                    
                    # 提交事务
                    conn.commit()
                except Exception as e:
                    # 回滚事务
                    conn.rollback()
                    raise e
            
            # 生成并存储向量嵌入（如果有嵌入模型）
            if db_connector.embedding_model is not None:
                self._generate_and_store_embedding(db_connector, topic_id)
            
            logging.info(f"Successfully saved competition topic: {self.title} (ID: {topic_id})")
            return topic_id
        
        except Exception as e:
            logging.error(f"Error saving competition topic: {str(e)}")
            raise
    
    def _generate_and_store_embedding(self, db_connector: DBConnector, topic_id: int) -> None:
        """生成并存储题目描述的向量嵌入
        
        Args:
            db_connector: 数据库连接器
            topic_id: 题目ID
        """
        # 准备用于生成嵌入的文本
        content = f"{self.title} {self.description}"
        if self.input_format:
            content += f" {self.input_format}"
        if self.output_format:
            content += f" {self.output_format}"
            
        # 生成嵌入向量
        embedding = db_connector.embedding_model.embed_query(content)
        
        # 存储嵌入向量
        embedding_data = {
            "topic_id": topic_id,
            "content": content,
            "embedding": embedding
        }
        
        db_connector.insert_data("backend_competition_topic_embeddings", embedding_data)
    
    @classmethod
    def from_db_record(cls, record: Dict[str, Any]) -> 'CompetitionTopicData':
        """从数据库记录创建模型实例
        
        Args:
            record: 数据库记录
            
        Returns:
            CompetitionTopicData: 创建的模型实例
        """
        # 处理 SQLite 返回的 JSON 字符串
        examples_data = record.get('examples')
        if isinstance(examples_data, str):
            try:
                examples_data = json.loads(examples_data)
            except json.JSONDecodeError:
                examples_data = []
        
        data_scales = record.get('data_scales')
        if isinstance(data_scales, str):
            try:
                data_scales = json.loads(data_scales)
            except json.JSONDecodeError:
                data_scales = []
        
        tags = record.get('tags')
        if isinstance(tags, str):
            try:
                tags = json.loads(tags)
            except json.JSONDecodeError:
                tags = []
        
        metadata = record.get('metadata')
        if isinstance(metadata, str):
            try:
                metadata = json.loads(metadata)
            except json.JSONDecodeError:
                metadata = {}
        
        # 创建样例对象
        examples = []
        if examples_data:
            for example in examples_data:
                examples.append(InputOutputExample(
                    input_example=example.get('input_example'),
                    output_example=example.get('output_example'),
                    explanation=example.get('explanation')
                ))
        
        # 创建数据规模对象
        data_scale = []
        if data_scales:
            for scale in data_scales:
                data_scale.append(DataScale(
                    test_point=scale.get('test_point'),
                    scale=scale.get('scale')
                ))
        
        # 创建模型实例
        return cls(
            title=record.get('title'),
            competition_name=record.get('competition_name'),
            problem_id=record.get('problem_id'),
            description=record.get('description'),
            input_format=record.get('input_format'),
            output_format=record.get('output_format'),
            examples=examples,
            notes=record.get('notes'),
            data_scale=data_scale,
            hints=record.get('hints'),
            created_at=record.get('created_at'),
            updated_at=record.get('updated_at'),
            difficulty=record.get('difficulty'),
            tags=tags,
            metadata=metadata
        )
    
    @classmethod
    def get_by_id(cls, db_connector: DBConnector, topic_id: int) -> Optional['CompetitionTopicData']:
        """根据ID获取竞赛题目
        
        Args:
            db_connector: 数据库连接器
            topic_id: 题目ID
            
        Returns:
            Optional[CompetitionTopicData]: 题目数据，不存在则返回None
        """
        try:
            if db_connector._is_using_supabase():
                sql = "SELECT * FROM backend_competition_topics_with_examples WHERE id = %s"
                results = db_connector.execute_sql(sql, [topic_id])
                result = results[0] if results else None
            else:
                # SQLite 查询
                sql = "SELECT * FROM backend_competition_topics_with_examples WHERE id = ?"
                cursor = db_connector.sqlite_conn.cursor()
                cursor.execute(sql, [topic_id])
                result = cursor.fetchone()
            
            if not result:
                return None
            
            return cls.from_db_record(dict(result))
        except Exception as e:
            logging.error(f"Error getting competition topic by ID {topic_id}: {str(e)}")
            raise
    
    @classmethod
    def search(cls, db_connector: DBConnector, query: str, limit: int = 10) -> List['CompetitionTopicData']:
        """搜索竞赛题目
        
        Args:
            db_connector: 数据库连接器
            query: 搜索关键词
            limit: 返回结果数量限制
            
        Returns:
            List[CompetitionTopicData]: 搜索结果列表
        """
        try:
            search_term = f"%{query}%"
            
            # 直接检查 sqlite_conn 是否存在，而不是调用 _is_using_supabase()
            if hasattr(db_connector, 'sqlite_conn') and db_connector.sqlite_conn is not None:
                # SQLite 查询
                sql = """
                SELECT * FROM backend_competition_topics_with_examples
                WHERE 
                    title LIKE ? OR
                    description LIKE ? OR
                    problem_id LIKE ?
                ORDER BY created_at DESC
                LIMIT ?
                """
                params = [search_term, search_term, search_term, limit]
                cursor = db_connector.sqlite_conn.cursor()
                cursor.execute(sql, params)
                results = cursor.fetchall()
                
                # 将结果转换为字典列表
                column_names = [description[0] for description in cursor.description]
                dict_results = []
                for row in results:
                    dict_results.append(dict(zip(column_names, row)))
                
                return [cls.from_db_record(r) for r in dict_results]
            else:
                # Supabase 查询
                sql = """
                SELECT * FROM backend_competition_topics_with_examples
                WHERE 
                    title ILIKE %s OR
                    description ILIKE %s OR
                    problem_id ILIKE %s
                ORDER BY created_at DESC
                LIMIT %s
                """
                params = [search_term, search_term, search_term, limit]
                results = db_connector.execute_sql(sql, params)
                return [cls.from_db_record(dict(r)) for r in results]
        except Exception as e:
            logging.error(f"Error searching competition topics: {str(e)}")
            # 返回空列表而不是抛出异常，这样程序可以继续运行
            return []
    
    @classmethod
    def search_by_vector(cls, db_connector: DBConnector, query: str, limit: int = 10) -> List['CompetitionTopicData']:
        """基于向量相似度搜索竞赛题目
        
        Args:
            db_connector: 数据库连接器
            query: 搜索文本
            limit: 返回结果数量限制
            
        Returns:
            List[CompetitionTopicData]: 搜索结果列表
        """
        if not db_connector.embedding_model:
            raise ValueError("Embedding model is required for vector search")
            
        # 生成查询文本的向量嵌入
        query_embedding = db_connector.embedding_model.embed_query(query)
        
        # 执行向量相似度搜索
        sql = """
        SELECT t.*, e.similarity
        FROM backend_competition_topics t
        JOIN (
            SELECT topic_id, 1 - (embedding <=> %s::vector) as similarity
            FROM backend_competition_topic_embeddings
            ORDER BY similarity DESC
            LIMIT %s
        ) e ON t.id = e.topic_id
        JOIN backend_competition_topics_with_examples te ON t.id = te.id
        ORDER BY e.similarity DESC
        """
        
        results = db_connector.execute_sql(sql, [query_embedding, limit])
        
        return [cls.from_db_record(dict(r)) for r in results]
    
    @classmethod
    def from_crawl_data(cls, crawl_data: Dict[str, Any]) -> 'CompetitionTopicData':
        """从爬虫数据创建模型实例
        
        Args:
            crawl_data: 爬虫数据
            
        Returns:
            CompetitionTopicData: 模型实例
        """
        # 处理样例数据
        examples = []
        if crawl_data.get("examples"):
            for example in crawl_data["examples"]:
                examples.append(InputOutputExample(
                    input_example=example.get("input", ""),
                    output_example=example.get("output", ""),
                    explanation=None
                ))
        
        # 处理标签
        tags = []
        if crawl_data.get("tags"):
            for tag_item in crawl_data["tags"]:
                if isinstance(tag_item, dict) and tag_item.get("tag_name"):
                    tags.append(tag_item["tag_name"])
                elif isinstance(tag_item, str):
                    tags.append(tag_item)
        
        # 创建实例
        return cls(
            title=crawl_data.get("title"),
            problem_id=crawl_data.get("problem_id"),
            description=crawl_data.get("description"),
            input_format=crawl_data.get("input_format"),
            output_format=crawl_data.get("output_format"),
            notes=crawl_data.get("notes"),
            difficulty=crawl_data.get("difficulty"),
            tags=tags,
            examples=examples
        )

    class Config:
        """Pydantic 模型配置"""
        orm_mode = True
        schema_extra = {
            "example": {
                "title": "直播获奖",
                "competition_name": "CSP-J2020",
                "problem_id": "P7072",
                "description": "NOI2130 即将举行。为了增加观赏性，CCF 决定逐一评出每个选手的成绩，并直播即时的获奖分数线。本次竞赛的获奖率为 w%，即当前排名前 w% 的选手的最低成绩就是即时的分数线。\n\n更具体地，若当前已评出了 p 个选手的成绩，则当前计划获奖人数为 max(1, ⌊p × w%⌋)，其中 w 是获奖百分比，⌊x⌋ 表示对 x 向下取整，max(x,y) 表示 x 和 y 中较大的数。如有选手成绩相同，则所有成绩并列的选手都能获奖，因此实际获奖人数可能比计划中多。\n\n作为评测组的技术人员，请你帮 CCF 写一个直播程序。",
                "input_format": "第一行有两个整数 n, w。分别代表选手总数与获奖率。\n第二行有 n 个整数，依次代表逐一评出的选手成绩。",
                "output_format": "只有一行，包含 n 个非负整数，依次代表选手成绩逐一评出后，即时的获奖分数线。相邻两个整数间用一个空格分隔。",
                "examples": [
                    {
                        "input_example": "10 60\n200 300 400 500 600 600 0 300 200 100",
                        "output_example": "200 300 400 400 400 500 400 400 300 300",
                        "explanation": "![](https://cdn.luogu.com.cn/upload/image_hosting/l453vhow.png)"
                    },
                    {
                        "input_example": "10 30\n100 100 600 100 100 100 100 100 100 100",
                        "output_example": "100 100 600 600 600 600 100 100 100 100"
                    }
                ],
                "notes": "### 数据规模与约定\n\n各测试点的 n 如下表：\n\n| 测试点编号 | n= |\n| :--: | :--: |\n| 1~3 | 10 |\n| 4~6 | 500 |\n| 7~10 | 2000 |\n| 11~17 | 10^4 |\n| 18~20 | 10^5 |",
                "data_scale": [
                    {"test_point": "1~3", "scale": "10"},
                    {"test_point": "4~6", "scale": "500"},
                    {"test_point": "7~10", "scale": "2000"},
                    {"test_point": "11~17", "scale": "10^4"},
                    {"test_point": "18~20", "scale": "10^5"}
                ],
                "hints": "在计算计划获奖人数时，如用浮点类型的变量（如 C/C++ 中的 float 、 double，Pascal 中的 real 、 double 、 extended 等）存储获奖比例 w%，则计算 5 × 60% 时的结果可能为 3.000001，也可能为 2.999999，向下取整后的结果不确定。因此，建议仅使用整型变量，以计算出准确值。",
                "created_at": "2024-03-15T14:30:00",
                "updated_at": "2024-03-15T14:30:00",
                "difficulty": "中等",
                "tags": ["模拟", "排序", "实现"],
                "metadata": {
                    "source": "洛谷",
                    "original_id": "P7072",
                    "time_limit": "1000ms",
                    "memory_limit": "128MB"
                }
            }
        }

def create_competition_topic_data(**data) -> CompetitionTopicData:
    """
    工厂函数，用于创建 CompetitionTopicData 实例
    
    Args:
        **data: CompetitionTopicData 模型的字段数据
        
    Returns:
        CompetitionTopicData: 创建的竞赛题目数据实例
    """
    return CompetitionTopicData(**data) 

"""
竞赛题目数据模型使用示例
"""

# from data_models.competition_topic_data import CompetitionTopicData, InputOutputExample, DataScale
# from utils.db_connector import DBConnector
from config import get_config
import asyncio
from common.sql.create_sqlite_competition_topic_tables import create_competition_database

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

async def main():
    # 步骤1: 创建数据库
    db_path = create_competition_database("data/competition_database.db")
    
    # 步骤2: 初始化数据库连接器
    db_connector = DBConnector(sqlite_path=db_path)
    
    # 步骤3: 创建竞赛题目
    topic = CompetitionTopicData(
        title="两数之和",
        competition_name="LeetCode",
        problem_id="LC001",
        description="给定一个整数数组 nums 和一个目标值 target，请你在该数组中找出和为目标值的那两个整数，并返回他们的数组下标。",
        input_format="第一行为数组 nums，第二行为目标值 target。",
        output_format="返回两个整数的下标。",
        examples=[
            InputOutputExample(
                input_example="nums = [2, 7, 11, 15], target = 9",
                output_example="[0, 1]",
                explanation="因为 nums[0] + nums[1] = 2 + 7 = 9，所以返回 [0, 1]"
            )
        ],
        data_scale=[
            DataScale(test_point="1~3", scale="n ≤ 10"),
            DataScale(test_point="4~6", scale="n ≤ 100")
        ],
        difficulty="简单",
        tags=["数组", "哈希表"],
        metadata={
            "source": "LeetCode",
            "time_limit": "1000ms",
            "memory_limit": "128MB"
        }
    )
    
    # 步骤4: 保存到数据库
    topic_id = topic.save_to_db(db_connector)
    print(f"保存的题目ID: {topic_id}")
    
    # 步骤5: 从数据库读取
    retrieved_topic = CompetitionTopicData.get_by_id(db_connector, topic_id)
    print(f"获取的题目标题: {retrieved_topic.title}")
    print(f"样例数量: {len(retrieved_topic.examples)}")
    print(f"第一个样例输入: {retrieved_topic.examples[0].input_example}")
    
    # 步骤6: 搜索题目
    search_results = CompetitionTopicData.search(db_connector, "数组", limit=5)
    print(f"搜索结果数量: {len(search_results)}")
    for result in search_results:
        print(f"- {result.title} ({result.problem_id})")
    
    # 步骤7: 批量插入多个题目
    topics = [
        CompetitionTopicData(
            title="最长回文子串",
            problem_id="LC005",
            description="给你一个字符串 s，找到 s 中最长的回文子串。",
            difficulty="中等",
            tags=["字符串", "动态规划"]
        ),
        CompetitionTopicData(
            title="合并两个有序链表",
            problem_id="LC021",
            description="将两个升序链表合并为一个新的升序链表并返回。",
            difficulty="简单",
            tags=["链表", "递归"]
        )
    ]
    
    for topic in topics:
        topic_id = topic.save_to_db(db_connector)
        print(f"保存题目: {topic.title}, ID: {topic_id}")
    
    # 步骤8: 统计数据库中的题目数量
    count_sql = "SELECT COUNT(*) FROM backend_competition_topics"
    if hasattr(db_connector, 'sqlite_conn') and db_connector.sqlite_conn is not None:
        # SQLite 直接查询
        cursor = db_connector.sqlite_conn.cursor()
        cursor.execute(count_sql)
        result = cursor.fetchone()
    else:
        # Supabase 查询
        results = db_connector.execute_sql(count_sql)
        result = results[0] if results else [0]

    print(f"数据库中的题目总数: {result[0]}")
    
    # 步骤9: 测试markdown格式输出前3条数据
    markdown_result = db_connector.execute_sql(
        "SELECT * FROM backend_competition_topics_with_examples LIMIT 3",
        as_markdown=True
    )
    print(markdown_result)
    
    # 步骤10: 测试json格式输出前3条数据
    json_result = db_connector.execute_sql_json(
        "SELECT * FROM backend_competition_topics_with_examples LIMIT 3"
    )
    print(json_result)

if __name__ == "__main__":
    asyncio.run(main())