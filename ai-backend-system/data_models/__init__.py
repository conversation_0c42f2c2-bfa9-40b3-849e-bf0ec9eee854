# ai-backend-system/data_models/__init__.py
"""
data_models 包的初始化模块.

该模块用于方便地导入 data_models 包中的所有数据模型类，例如 ProductData, DealData 等.
在其他模块中，可以通过 `from ai_backend_system.data_models import ProductData, DealData` 导入这些数据模型类.
"""
from .content_type import ContentType
from .content_data_model import ContentDataModel
from .deal_data import DealData
from .product_data import ProductData
from .comment_data import CommentData
from .search_result_data import SearchResultData
from .video_data import VideoData
from .article_data import ArticleData, ArticleStatistics
from .base_functions import get_region_list, validate_region, create_model_instance, get_model_example
from .competition_topic_data import CompetitionTopicData
__all__ = [
    "ContentType",
    "ContentDataModel",
    "DealData",
    "ProductData",
    "CommentData",
    "SearchResultData",
    "VideoData",
    "ArticleData",
    "ArticleStatistics",
    "CompetitionTopicData",
    "get_region_list",
    "validate_region",
    "create_model_instance",
    "get_model_example",
]

