"""
ContentDataModel 抽象基类定义。

该模块定义了所有内容数据模型的基类，提供了通用的接口和工具方法。
"""

from abc import ABC, abstractmethod
from pydantic import BaseModel
from typing import Type, Dict, ClassVar
from data_models.content_type import ContentType


class ContentDataModel(ABC, BaseModel):
    """内容数据模型抽象基类"""
    content_type: ContentType = None
    
    @abstractmethod
    def export_prompts(self) -> str:
        """导出数据模型的 prompts for LLM to understand the data model"""
        pass

    # 类变量，存储内容类型到模型类的映射
    _model_map: ClassVar[Dict[ContentType, Type["ContentDataModel"]]] = {}

    @classmethod
    def get_model_class(cls, content_type: ContentType) -> Type["ContentDataModel"]:
        """根据内容类型返回对应的数据模型类
        
        Args:
            content_type: ContentType 枚举值
            
        Returns:
            对应的数据模型类
            
        Raises:
            ValueError: 当传入未知的内容类型时
        """
        # 如果映射字典为空，则初始化
        if not cls._model_map:
            from .deal_data import DealData
            from .product_data import ProductData
            from .comment_data import CommentData
            from .article_data import ArticleData
            from .video_data import VideoData
            from .search_result_data import SearchResultData
            from .competition_topic_data import CompetitionTopicData
            
            # 动态构建映射
            for model_class in [DealData, ProductData, CommentData, ArticleData, VideoData, SearchResultData, CompetitionTopicData]:
                if not model_class.content_type:
                    raise ValueError(f"Content type not defined for {model_class.__name__}")
                cls._model_map[model_class.content_type] = model_class
        
        if content_type not in cls._model_map:
            raise ValueError(f"Unknown content type: {content_type}")
            
        return cls._model_map[content_type]