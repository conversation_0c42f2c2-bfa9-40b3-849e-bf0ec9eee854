"""
定义内容收集助手 Agent (ContentCollectionAssistantAgent) 类。

该 Agent 负责：
- 接收运营经理 Agent (OperationalManagerAgent) 发送的内容收集任务
- 根据任务类型调用相应的内容收集子 Agent (DealCollector, ProductInfoCollector, CommentCollector, ArticleCollector, VideoCollector)
- 驱动爬虫工程师 Agent (CrawlerEngineerAgent) 进行数据采集
- 对采集到的数据进行初步处理和格式化
- 将处理后的数据传递给其他 Agent (如内容生成专员、内容评审专员等)

核心功能包括：
- 收集 Deal 信息 (价格、折扣、链接等)
- 收集产品信息 (规格、特性、评价等)
- 收集用户评论 (评分、内容、时间等)
- 收集文章内容 (标题、正文、作者等)
- 收集视频内容 (标题、链接、时长等)
- 数据预处理和格式化
"""
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import os
import logging
import json
from data_models.content_type import ContentType
from data_models.content_data_model import ContentDataModel
from config import get_config

class ContentCollectorAgent(ABC):
    """内容收集 Agent 抽象基类"""
    
    def __init__(self, name: str, model_client=None, system_message: str = None, region: str = ""):
        """
        初始化内容收集 Agent。
        
        Args:
            name (str): Agent 名称
            model_client: LLM 模型客户端
            system_message (str, optional): 系统消息
            region (str): 地区代码，必须是配置文件中定义的有效区域
        """
        self.name = name
        self.agent = AssistantAgent(
            name=name,
            system_message=system_message or self.get_default_system_message(),
            model_client=model_client
        )
        self.region = region.lower() if region else ""
    
    @abstractmethod
    def get_default_system_message(self) -> str:
        """获取默认系统消息"""
        pass
    
    @abstractmethod
    def collect_content(self, source_url: str, **kwargs) -> Dict[str, Any]:
        """
        收集内容的抽象方法
        
        Args:
            source_url (str): 源URL
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 收集到的内容
        """
        pass
    
    def preprocess_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理采集到的原始数据
        
        Args:
            raw_data (Dict[str, Any]): 原始数据
            
        Returns:
            Dict[str, Any]: 预处理后的数据
        """
        # 默认实现，子类可以重写
        return raw_data


class DealCollectorAgent(ContentCollectorAgent):
    """Deal 收集 Agent"""
    
    def get_default_system_message(self) -> str:
        return """You are a Deal Collection Assistant specialized in gathering deal information from various e-commerce platforms.
Your responsibilities include:
1. Identifying valid deals from websites
2. Extracting key deal information (price, discount, availability)
3. Verifying deal accuracy and completeness
4. Formatting deal data according to system requirements"""
    
    def collect_content(self, source_url: str, **kwargs) -> Dict[str, Any]:
        """
        收集 Deal 信息
        
        Args:
            source_url (str): Deal 来源 URL
            **kwargs: 其他参数，如区域、类别等
            
        Returns:
            Dict[str, Any]: 收集到的 Deal 信息
        """
        logging.info(f"Collecting deal information from {source_url}")
        
        # 调用爬虫工程师 Agent 进行数据采集
        # 这里假设 crawler_agent 已经在其他地方定义并传入
        crawler_agent = kwargs.get('crawler_agent')
        if not crawler_agent:
            raise ValueError("Crawler agent is required for deal collection")
        
        # 构建爬虫参数
        crawl_params = {
            'url': source_url,
            'content_type': ContentType.DEAL,
            'use_proxy': kwargs.get('use_proxy', False)
        }
        
        # 执行爬虫任务
        raw_data = crawler_agent.crawl(**crawl_params)
        
        # 预处理数据
        processed_data = self.preprocess_data(raw_data)
        
        return processed_data
    
    def preprocess_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理 Deal 数据
        
        Args:
            raw_data (Dict[str, Any]): 原始 Deal 数据
            
        Returns:
            Dict[str, Any]: 预处理后的 Deal 数据
        """
        # 处理价格格式
        if 'current_price' in raw_data and isinstance(raw_data['current_price'], str):
            raw_data['current_price'] = self._extract_price(raw_data['current_price'])
        
        if 'original_price' in raw_data and isinstance(raw_data['original_price'], str):
            raw_data['original_price'] = self._extract_price(raw_data['original_price'])
        
        # 处理图片链接
        if 'thumb_image_url' in raw_data and raw_data['thumb_image_url']:
            raw_data['thumb_image_url'] = self._ensure_full_url(raw_data['thumb_image_url'])
        
        # 处理来源网站
        if 'source_site' not in raw_data or not raw_data['source_site']:
            raw_data['source_site'] = self._extract_domain(source_url)
        
        return raw_data
    
    def _extract_price(self, price_str: str) -> float:
        """从价格字符串中提取数值"""
        import re
        price_match = re.search(r'[\d,.]+', price_str)
        if price_match:
            # 移除千位分隔符并将小数点转换为点
            price_clean = price_match.group(0).replace(',', '')
            return float(price_clean)
        return 0.0
    
    def _ensure_full_url(self, url: str) -> str:
        """确保 URL 是完整的"""
        if url and not (url.startswith('http://') or url.startswith('https://')):
            return f"https:{url}" if url.startswith('//') else f"https://{url}"
        return url
    
    def _extract_domain(self, url: str) -> str:
        """从 URL 中提取域名"""
        import re
        domain_match = re.search(r'https?://(?:www\.)?([^/]+)', url)
        if domain_match:
            return domain_match.group(1)
        return ""


class ProductInfoCollectorAgent(ContentCollectorAgent):
    """产品信息收集 Agent"""
    
    def get_default_system_message(self) -> str:
        return """You are a Product Information Collection Assistant specialized in gathering detailed product data.
Your responsibilities include:
1. Extracting comprehensive product specifications
2. Gathering product features and benefits
3. Collecting product images and media
4. Organizing product data in a structured format"""
    
    def collect_content(self, source_url: str, **kwargs) -> Dict[str, Any]:
        """
        收集产品信息
        
        Args:
            source_url (str): 产品页面 URL
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 收集到的产品信息
        """
        logging.info(f"Collecting product information from {source_url}")
        
        # 调用爬虫工程师 Agent 进行数据采集
        crawler_agent = kwargs.get('crawler_agent')
        if not crawler_agent:
            raise ValueError("Crawler agent is required for product information collection")
        
        # 构建爬虫参数
        crawl_params = {
            'url': source_url,
            'content_type': ContentType.PRODUCT,
            'use_proxy': kwargs.get('use_proxy', False)
        }
        
        # 执行爬虫任务
        raw_data = crawler_agent.crawl(**crawl_params)
        
        # 预处理数据
        processed_data = self.preprocess_data(raw_data)
        
        return processed_data


class CommentCollectorAgent(ContentCollectorAgent):
    """评论收集 Agent"""
    
    def get_default_system_message(self) -> str:
        return """You are a Comment Collection Assistant specialized in gathering user feedback and reviews.
Your responsibilities include:
1. Collecting user comments from various platforms
2. Extracting sentiment and key points from comments
3. Identifying trending topics in user discussions
4. Organizing comment data for analysis"""
    
    def collect_content(self, source_url: str, **kwargs) -> List[Dict[str, Any]]:
        """
        收集评论信息
        
        Args:
            source_url (str): 评论页面 URL
            **kwargs: 其他参数，如评论数量限制
            
        Returns:
            List[Dict[str, Any]]: 收集到的评论列表
        """
        logging.info(f"Collecting comments from {source_url}")
        
        # 调用爬虫工程师 Agent 进行数据采集
        crawler_agent = kwargs.get('crawler_agent')
        if not crawler_agent:
            raise ValueError("Crawler agent is required for comment collection")
        
        # 构建爬虫参数
        crawl_params = {
            'url': source_url,
            'content_type': ContentType.COMMENT,
            'use_proxy': kwargs.get('use_proxy', False),
            'limit': kwargs.get('limit', 50)  # 默认收集50条评论
        }
        
        # 执行爬虫任务
        raw_data = crawler_agent.crawl(**crawl_params)
        
        # 预处理数据
        processed_data = self.preprocess_data(raw_data)
        
        return processed_data


class ArticleCollectorAgent(ContentCollectorAgent):
    """文章收集 Agent"""
    
    def get_default_system_message(self) -> str:
        return """You are an Article Collection Assistant specialized in gathering comprehensive article content.
Your responsibilities include:
1. Extracting full article text while preserving formatting
2. Collecting article metadata (author, date, category)
3. Identifying and extracting embedded media
4. Organizing article content in a structured format"""
    
    def collect_content(self, source_url: str, **kwargs) -> Dict[str, Any]:
        """
        收集文章内容
        
        Args:
            source_url (str): 文章 URL
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 收集到的文章内容
        """
        logging.info(f"Collecting article content from {source_url}")
        
        # 调用爬虫工程师 Agent 进行数据采集
        crawler_agent = kwargs.get('crawler_agent')
        if not crawler_agent:
            raise ValueError("Crawler agent is required for article collection")
        
        # 构建爬虫参数
        crawl_params = {
            'url': source_url,
            'content_type': ContentType.ARTICLE,
            'use_proxy': kwargs.get('use_proxy', False)
        }
        
        # 执行爬虫任务
        raw_data = crawler_agent.crawl(**crawl_params)
        
        # 预处理数据
        processed_data = self.preprocess_data(raw_data)
        
        return processed_data


class VideoCollectorAgent(ContentCollectorAgent):
    """视频收集 Agent"""
    
    def get_default_system_message(self) -> str:
        return """You are a Video Collection Assistant specialized in gathering video content and metadata.
Your responsibilities include:
1. Extracting video metadata (title, creator, duration)
2. Collecting video statistics (views, likes, comments)
3. Gathering video descriptions and tags
4. Organizing video data in a structured format"""
    
    def get_default_system_message(self) -> str:
        return "You are a Video Collection Assistant. You collect video content from various platforms."
    
    def collect_content(self, source_url: str, **kwargs) -> Dict[str, Any]:
        """
        收集视频内容
        
        Args:
            source_url (str): 视频 URL
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 收集到的视频内容
        """
        logging.info(f"Collecting video content from {source_url}")
        
        # 调用爬虫工程师 Agent 进行数据采集
        crawler_agent = kwargs.get('crawler_agent')
        if not crawler_agent:
            raise ValueError("Crawler agent is required for video collection")
        
        # 构建爬虫参数
        crawl_params = {
            'url': source_url,
            'content_type': ContentType.VIDEO,
            'use_proxy': kwargs.get('use_proxy', False)
        }
        
        # 执行爬虫任务
        raw_data = crawler_agent.crawl(**crawl_params)
        
        # 预处理数据
        processed_data = self.preprocess_data(raw_data)
        
        return processed_data


def get_region_list() -> List[str]:
    """
    从配置文件获取支持的区域列表
    
    Returns:
        List[str]: 支持的区域代码列表
    """
    config = get_config()
    regions = config.get('regions', [])
    if not regions:
        # 默认区域列表，仅在配置文件中未定义时使用
        logging.warning("No regions defined in config.yaml, using default regions")
        return ['us', 'uk', 'au', 'ca', 'de', 'fr', 'jp']
    return regions

class ContentCollectionAssistantAgent:
    """内容收集助手 Agent，协调各类内容收集子 Agent"""
    
    def __init__(self, name="content_collection_assistant", region=""):
        """
        初始化内容收集助手 Agent
        
        Args:
            name (str): Agent 名称
            region (str): 地区代码，必须是配置文件中定义的有效区域
        """
        # 验证区域是否有效
        supported_regions = get_region_list()
        if region and region.lower() not in [r.lower() for r in supported_regions]:
            logging.warning(f"Region '{region}' not in configured regions: {supported_regions}. Using default region.")
            region = supported_regions[0] if supported_regions else ""
        
        self.name = name
        self.region = region.lower() if region else ""
        
        # 创建模型客户端
        model_client = OpenAIChatCompletionClient(
            model="gemini-2.0-flash-exp", 
            api_key=os.getenv('GEMINI_API_KEY'),
            model_info={
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": "unknown",
            })
        
        # 构建系统消息，包含区域信息
        region_specific_message = f" for {self.region.upper()} region" if self.region else ""
        system_message = f"""You are a Content Collection Coordinator{region_specific_message} responsible for orchestrating various content collection tasks.
Your responsibilities include:
1. Analyzing content collection requests
2. Delegating tasks to specialized collector agents
3. Monitoring collection progress
4. Ensuring data quality and completeness
5. Reporting collection results"""
        
        if self.region:
            system_message += f"\n\nYou specialize in content from the {self.region.upper()} region and understand its specific market characteristics."
        
        self.agent = AssistantAgent(
            name=f"{name}_{self.region}" if self.region else name,
            system_message=system_message,
            model_client=model_client
        )
        
        # 初始化各类内容收集子 Agent
        self.collectors = {
            ContentType.DEAL: DealCollectorAgent(f"deal_collector_{self.region}" if self.region else "deal_collector", model_client, region=self.region),
            ContentType.PRODUCT: ProductInfoCollectorAgent("product_collector", model_client),
            ContentType.COMMENT: CommentCollectorAgent(f"comment_collector_{self.region}" if self.region else "comment_collector", model_client, region=self.region),
            ContentType.ARTICLE: ArticleCollectorAgent(f"article_collector_{self.region}" if self.region else "article_collector", model_client, region=self.region),
            ContentType.VIDEO: VideoCollectorAgent(f"video_collector_{self.region}" if self.region else "video_collector", model_client, region=self.region)
        }
    
    def collect_content(self, content_type: ContentType, source_url: str, crawler_agent=None, **kwargs) -> Dict[str, Any]:
        """
        收集指定类型的内容
        
        Args:
            content_type (ContentType): 内容类型
            source_url (str): 源 URL
            crawler_agent: 爬虫工程师 Agent
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 收集到的内容
        """
        if content_type not in self.collectors:
            raise ValueError(f"Unsupported content type: {content_type}")
        
        if not crawler_agent:
            raise ValueError("Crawler agent is required for content collection")
        
        # 调用相应的内容收集子 Agent
        collector = self.collectors[content_type]
        collected_data = collector.collect_content(
            source_url=source_url,
            crawler_agent=crawler_agent,
            **kwargs
        )
        
        # 验证数据格式
        self._validate_data(content_type, collected_data)
        
        return collected_data
    
    def _validate_data(self, content_type: ContentType, data: Dict[str, Any]) -> None:
        """
        验证数据格式是否符合要求
        
        Args:
            content_type (ContentType): 内容类型
            data (Dict[str, Any]): 待验证的数据
            
        Raises:
            ValueError: 数据格式不符合要求时抛出
        """
        # 获取对应的数据模型类
        model_class = ContentDataModel.get_model_class(content_type)
        
        try:
            # 尝试使用模型类验证数据
            model_instance = model_class(**data)
        except Exception as e:
            logging.error(f"Data validation failed for {content_type}: {str(e)}")
            raise ValueError(f"Collected data does not match the required format for {content_type}: {str(e)}")


def create_content_collection_assistant(region: Optional[str] = None):
    """
    工厂函数，创建内容收集助手 Agent
    
    Args:
        region (Optional[str]): 地区代码，例如 'us', 'uk', 'au' 等。
                               如果未提供或提供的区域不在配置中，将使用配置中的第一个区域
        
    Returns:
        ContentCollectionAssistantAgent: 内容收集助手 Agent 实例
    """
    # 从配置中获取支持的区域列表
    supported_regions = get_region_list()
    
    # 如果未提供区域或提供的区域不在支持列表中，使用第一个支持的区域
    if not region or region.lower() not in [r.lower() for r in supported_regions]:
        if not supported_regions:
            raise ValueError("No regions defined in configuration")
        region = supported_regions[0]
        logging.info(f"Using default region from config: {region}")
    else:
        logging.info(f"Using specified region: {region}")
    
    return ContentCollectionAssistantAgent(region=region)

def create_content_collection_assistants_for_all_regions() -> Dict[str, ContentCollectionAssistantAgent]:
    """
    为配置中的所有区域创建内容收集助手 Agent
    
    Returns:
        Dict[str, ContentCollectionAssistantAgent]: 区域代码到助手 Agent 的映射
    """
    # 从配置中获取支持的区域列表
    supported_regions = get_region_list()
    
    # 为每个区域创建助手 Agent
    assistants = {}
    for region in supported_regions:
        try:
            assistant = create_content_collection_assistant(region)
            assistants[region] = assistant
            logging.info(f"Created content collection assistant for region: {region}")
        except Exception as e:
            logging.error(f"Failed to create content collection assistant for region {region}: {str(e)}")
    
    return assistants

# 示例用法
if __name__ == "__main__":
    # 创建特定区域的助手
    au_assistant = create_content_collection_assistant("au")
    
    # 创建所有区域的助手
    all_assistants = create_content_collection_assistants_for_all_regions()
    
    # 使用特定区域的助手
    us_assistant = all_assistants.get("us")
    if us_assistant:
        # 使用助手进行操作
        pass 