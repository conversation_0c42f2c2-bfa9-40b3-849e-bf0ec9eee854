# ai-backend-system/agents/customer_service_team/__init__.py
"""
customer_service_team 包的初始化模块。

该模块包含了客户服务团队所有 Agent 共用的导入和配置。
配置日志记录器。
"""

# 通用导入
from typing import Optional, Dict, Any, List, Awaitable
import json
import logging
import os
import asyncio
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler

# AutoGen 相关导入
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.memory import MemoryContent, MemoryMimeType, MemoryQueryResult

# 项目相关导入
from config import get_config
from utils.memory.multi_user_memory import (
    MultiUserMemory,
    MultiUserMemoryConfig,
    MemoryLevel
)

# 导出所有 Agent 类
from .teaching_assistant_agent import TeachingAssistantAgent

__all__ = [
    'TeachingAssistantAgent',
    'create_teaching_assistant_agent',
]

def setup_logger():
    """配置日志记录器"""
    config = get_config()
    log_config = config.get('logging', {})
    
    # 创建logs目录
    log_dir = 'logs'
    os.makedirs(log_dir, exist_ok=True)
    
    # 获取日志配置，如果没有则使用默认值
    log_level = getattr(logging, log_config.get('log_level', 'INFO'))
    log_file = log_config.get('log_file_path', 'logs/customer_service.log')
    log_format = log_config.get('log_format', '%(asctime)s - %(levelname)s - %(module)s - %(message)s')
    
    # 创建格式化器
    formatter = logging.Formatter(log_format)
    
    # 创建并配置文件处理器（带有文件轮转）
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    
    # 创建并配置控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

# 在模块导入时设置日志记录器
setup_logger()
