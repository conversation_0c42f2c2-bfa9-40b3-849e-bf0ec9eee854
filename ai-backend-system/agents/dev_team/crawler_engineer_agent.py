"""
CrawlerEngineerAgent - 爬虫工程师代理

该代理负责:
1. 基于Crawl4AI的WebCrawler进行数据抓取
2. 基于Crawl4AI的Schema进行数据解析
3. 维护和更新指定URL的Schema
4. 响应其他Agent的数据抓取请求
"""

import logging
import json
import os
import asyncio
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from config import get_config
from data_models import ContentType, ContentDataModel
from enum import Enum
from typing import Optional, Dict, Any, List
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.async_configs import LLMConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from urllib.parse import urlparse


class ParseType(Enum):
    ITEM_LIST = "item_list"
    ITEM_DETAIL = "item_detail"
        
class CrawlerEngineerAgent(AssistantAgent):
    """爬虫工程师代理，负责爬虫开发和数据抓取"""
    
    def __init__(
        self,
        name: str = "crawler_engineer",
        system_message: str = """You are a crawler engineer specialized in web scraping and data extraction.
Your main responsibilities include:
* 直接调用给定tool进行爬取，只要其成功返回你就认为任务完成.
Reply with TERMINATE when the task has been completed.""",
        model_client: Optional[OpenAIChatCompletionClient] = None,
        tools: Optional[List[Any]] = None,
    ) -> None:
        """初始化爬虫工程师代理
        
        Args:
            name: 代理名称
            system_message: 系统提示消息
            model_client: 模型客户端，默认使用 Gemini
            tools: 工具列表
        """
        if model_client is None:
            model_client = OpenAIChatCompletionClient(
                model=get_config()['agents']['crawler_engineer']['agent_model_name'],
                api_key=get_config()['agents']['crawler_engineer']['agent_model_api_key'], 
                # Temperory model info
                # TODO: remove this after testing
                model_info={
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "family": "unknown",
                }
            )

        # 定义爬虫相关工具
        if tools is None:
            tools = [
                self._crawl_and_parsing_result,
            ]

        super().__init__(
            name=name,
            system_message=system_message,
            model_client=model_client,
            tools=tools,
            reflect_on_tool_use=True
        )

    async def _crawl_and_parsing_result(self, target_url: str, content_type: ContentType, parse_type: ParseType) -> str:
        """爬取指定URL的数据并解析结果, 首先检测是否存在schema（通过_get_schema_path获取）, 如果存在则直接返回schema并调用_crawl_url进行爬取和解析, 
        否则调用_generate_schema_by_type生成schema并保存到本地，再次调用_crawl_url进行爬取和解析并返回"""
        schema_path = self._get_schema_path(target_url, content_type, parse_type)
        if os.path.exists(schema_path):
            #schema = json.load(open(schema_path, 'r', encoding='utf-8'))
            return await self._crawl_url(target_url, schema_path=schema_path, result_type="extracted_json")
        else:
            html_content = await self._crawl_url(target_url, result_type="raw_html")
            schema_path = await self._generate_schema_by_type(target_url, html_content, content_type, parse_type)
            return await self._crawl_url(target_url, schema_path=schema_path, result_type="extracted_json")

            
    async def _generate_schema_by_type(self, target_url: str, html_content: str, content_type: ContentType, parse_type: ParseType) -> str:
        """基于Craw4AI的Schema生成器（JsonCssExtractionStrategy）根据HTML内容生成Schema，并根据url和content_type保存到本地"""
        
        # 获取对应的数据模型类
        model_class = ContentDataModel.get_model_class(content_type)
        
        # 创建一个实例来调用实例方法
        model_instance = model_class()
        
        try:
            query_str = """You are an expert in generating JSON schemas for web scraping. Your task is to create a schema that can be used to extract specific information related to deals from a web page. The schema should be designed to match only the relevant entries on the page.


The information we want to extract includes the following fields:""" + model_instance.export_prompts()
            css_schema = JsonCssExtractionStrategy.generate_schema(
                html=html_content,
                schema_type="CSS",
                llm_config=LLMConfig(
                    provider=get_config()['agents']['crawler_engineer']['extractor_model_name'],
                    api_token=get_config()['agents']['crawler_engineer']['extractor_model_api_key'],
                ),
                query=query_str,
            )
            # 错误处理并logging
            if css_schema is None:
                logging.error(f"Failed to generate schema for {target_url}")
                return None

            schema_path = self._get_schema_path(target_url, content_type, parse_type)
            if isinstance(css_schema, dict):
                css_schema = json.dumps(css_schema, indent=2)
            with open(schema_path, 'w', encoding='utf-8') as f:
                f.write(css_schema)
            return schema_path
        except Exception as e:
            logging.error(f"Failed to generate schema for {target_url}: {e}")
            return None
        
    async def _crawl_url(self, url: str, use_proxy: bool = True, schema_path: str = None, result_type: str = "raw_html") -> Dict[str, Any]:
        """爬取指定URL的数据
        
        Args:
            url: 目标URL
            use_proxy: 是否允许使用代理重试
            schema_path: 可选的schema文件路径
            result_type: 可选的返回结果类型，可以是"raw_html"或"extracted_json"
            
        Returns:
            Dict[str, Any]: 爬取的数据
        """
        browser_cfg = BrowserConfig(
            verbose=True,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
            }
        )
        css_schema = None
        if schema_path is not None:
            with open(schema_path, 'r', encoding='utf-8') as f:
                css_schema = json.load(f)
        strategy = JsonCssExtractionStrategy(schema=css_schema, verbose=True) if css_schema else None
        crawl_config = CrawlerRunConfig(
            extraction_strategy=strategy,
            word_count_threshold=5,
            wait_for_images=True,
            exclude_external_links=False,
            remove_overlay_elements=True,
            scan_full_page=True,
            scroll_delay=0.5,
            cache_mode=CacheMode.DISABLED,
            page_timeout=30000,
            wait_until='commit'
        )

        # 根据use_proxy参数决定使用直接爬取还是代理爬取
        target_url = url
        if use_proxy:
            config = get_config()
            target_url = f"http://api.scrape.do?token={config['api_keys']['scrape_do_api_key']}&url={url}"
            logging.info(f"Using proxy for URL: {url}")
        
        try:
            logging.info(f"Crawling URL: {url}" + (" (with proxy)" if use_proxy else ""))
            async with AsyncWebCrawler(config=browser_cfg) as crawler:
                result = await crawler.arun(target_url, extraction_strategy=strategy, config=crawl_config)

                if not result.success:
                    raise Exception(f"Crawl failed for URL {url}: {result.error_message}")
                
                logging.info(f"Crawl successful for URL: {url}")
                
                if result_type == "raw_html":
                    return result.html
                elif result_type == "extracted_json":
                    return result.extracted_content
                else:
                    raise ValueError(f"Invalid result_type: {result_type}")
        except Exception as e:
            logging.error(f"Crawl failed for URL {url}: {str(e)}")
            raise



    def _get_schema_path(self, target_url: str, content_type: ContentType, parse_type: ParseType) -> str:
        """获取Schema文件路径
        
        Args:
            target_url: 目标URL
            content_type: 内容类型
            parse_type: 解析类型
        Returns:
            str: Schema文件的完整路径
        """
        parsed_url = urlparse(target_url)
        domain = parsed_url.netloc
        path = parsed_url.path.strip('/')
        
        # 创建schema目录
        schema_dir = os.path.join('schemas', domain)
        os.makedirs(schema_dir, exist_ok=True)
        
        if parse_type == ParseType.ITEM_LIST:
            # 处理列表页面，提取主路径作为文件名前缀
            path_prefix = path.split('/')[0] if path else parsed_url.query.split('&')[0].split('=')[0]
            filename = f"{path_prefix}_{content_type.value}.json"
        else:  # ParseType.ITEM_DETAIL
            # 处理详情页面，提取最后一层路径的父级作为文件名前缀
            path_parts = path.split('/')
            if len(path_parts) >= 2:
                path_prefix = path_parts[-2]
            else:
                path_prefix = path_parts[0]
            filename = f"{path_prefix}_{content_type.value}.json"
            
        return os.path.join(schema_dir, filename)

# 测试爬虫工程师代理
if __name__ == "__main__":
    from autogen_agentchat.teams import MagenticOneGroupChat, SelectorGroupChat, RoundRobinGroupChat
    from autogen_agentchat.ui import Console
    from autogen_agentchat.conditions import TextMentionTermination
    import asyncio
    from data_models import ContentType
    
    async def test_crawlers():
        # 初始化爬虫工程师代理
        crawler_engineer = CrawlerEngineerAgent()
        
        termination = TextMentionTermination("TERMINATE")
        # 创建团队聊天
        team = RoundRobinGroupChat(
            participants=[crawler_engineer],
            # model_client=OpenAIChatCompletionClient(
            #     model=get_config()['agents']['content_operation_manager']['testing_mode_name'],
            #     api_key=get_config()['agents']['content_operation_manager']['testing_mode_api_key'],
            #     # Temperory model info
            #     # TODO: remove this after gemini exp is supported by autogen
            #     model_info={
            #         "vision": False,
            #         "function_calling": True,
            #         "json_output": True,
            #         "family": "unknown",
            #     }
            # ),
            termination_condition=termination
        )
        
        # # 测试用例1: OzBargain
        # print("Testing OzBargain crawler...")
        # try:
        #     await Console(team.run_stream(
        #         task=f"""Please crawl and parse deals from OzBargain using this URL.
        #          As the orchestrator, you should determine the paging rules according to the URL and tell the agent the next page URL until reaching the given max page number.
        #          **Crucially, if you find even a single deal that is older than the cutoff time, you MUST immediately terminate the task.**
        #          No other requests need to be communicated to the agent.
        #          If the agent indicates that a page has been crawled, you can terminate the task.

        #          Start page URL: https://www.ozbargain.com.au/deals?page=0
        #          Cutoff time (terminate task if any deal on the crawled page is older than this time): 08/03/2025 - 23:06
        #          Max paging number (The maximum number of pages to crawl in total): 1
        #          Use content type: {ContentType.DEAL.value}"""
        #     ))
        # except Exception as e:
        #     print(f"OzBargain test failed: {str(e)}")
            
        # 测试用例2: CamelCamelCamel
        print("\nTesting CamelCamelCamel crawler...")
        try:
            await Console(team.run_stream(
                task=f"""Please crawl and parse the deals from CamelCamelCamel using this URL, 
                as the orchestrator, you should to determine the paging rules according to the url
                and tells the agent next page url until reach the given max page number.
                Last, if the agent tells you that the page is crawled, you can terminate the task without
                any other requests no need to tell the agent to do any other thing: 
                https://au.camelcamelcamel.com/popular?p=1
                Use content type: {ContentType.DEAL.value},
                Use parse type: {ParseType.ITEM_LIST.value}
                Max page number: 1"""
            ))
        except Exception as e:
            print(f"CamelCamelCamel test failed: {str(e)}")

        # 测试用例3: Luogu
        print("\nTesting Luogu crawler...")
        try:
            await Console(team.run_stream(
                task=f"""Please crawl and parse the competition topic from Luogu using this URL: 
                https://www.luogu.com.cn/problem/P1005
                 
                 Instructions:
                 1. Use the _crawl_and_parsing_result tool to extract the data
                 2. Use content type: {ContentType.COMPETITION_TOPIC.value}
                 3. Use parse type: {ParseType.ITEM_DETAIL.value}
                 4. The tool will automatically extract all required fields based on the CompetitionTopicData model
                 5. Once the data is extracted, terminate the task
                 
                 No need to provide HTML source code or explain the parsing process - just execute the crawling task."""
            ))
        except Exception as e:
            print(f"Luogu test failed: {str(e)}")

    # 运行测试
    asyncio.run(test_crawlers())