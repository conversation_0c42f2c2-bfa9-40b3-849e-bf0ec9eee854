#!/usr/bin/env python3
"""
查看当前使用教学助手服务的用户列表

此脚本通过检查 ChromaDB 数据库目录来获取当前使用服务的用户列表。
"""

import os
import sys
import json
import argparse
from datetime import datetime
import chromadb
from chromadb.config import Settings

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

def get_memory_path():
    """获取记忆数据库路径"""
    return os.path.join(project_root, "data", "memories")

def list_users(verbose=False):
    """
    列出所有使用服务的用户
    
    Args:
        verbose: 是否显示详细信息
    """
    memory_path = get_memory_path()
    
    if not os.path.exists(memory_path):
        print(f"记忆数据库路径不存在: {memory_path}")
        return
    
    print(f"记忆数据库路径: {memory_path}")
    
    # 获取所有用户目录
    user_dirs = []
    for item in os.listdir(memory_path):
        item_path = os.path.join(memory_path, item)
        if os.path.isdir(item_path) and not item.startswith('.'):
            user_dirs.append(item)
    
    if not user_dirs:
        print("未找到任何用户")
        return
    
    print(f"找到 {len(user_dirs)} 个用户:")
    
    # 遍历每个用户目录
    for user_id in sorted(user_dirs):
        user_path = os.path.join(memory_path, user_id)
        
        # 获取用户最后活动时间
        last_modified = datetime.fromtimestamp(os.path.getmtime(user_path))
        last_modified_str = last_modified.strftime("%Y-%m-%d %H:%M:%S")
        
        # 尝试连接用户的 ChromaDB 数据库
        try:
            client = chromadb.PersistentClient(
                path=user_path,
                settings=Settings(anonymized_telemetry=False)
            )
            
            # 获取所有集合
            collections = client.list_collections()
            collection_names = [c.name for c in collections]
            
            # 获取记忆数量
            memory_count = 0
            for collection in collections:
                try:
                    # 获取集合中的所有记忆
                    memories = collection.get()
                    memory_count += len(memories['ids']) if 'ids' in memories else 0
                except Exception as e:
                    if verbose:
                        print(f"  无法获取集合 {collection.name} 的记忆: {e}")
            
            # 打印用户信息
            print(f"用户ID: {user_id}")
            print(f"  最后活动时间: {last_modified_str}")
            print(f"  记忆集合: {', '.join(collection_names)}")
            print(f"  记忆数量: {memory_count}")
            
            if verbose:
                # 显示详细信息
                for collection in collections:
                    try:
                        memories = collection.get()
                        if 'metadatas' in memories and memories['metadatas']:
                            print(f"  集合 {collection.name} 中的记忆:")
                            for i, metadata in enumerate(memories['metadatas']):
                                if i < 5:  # 只显示前5条记忆
                                    created_at = metadata.get('created_at', 'unknown')
                                    memory_type = metadata.get('memory_type', 'unknown')
                                    print(f"    - [{created_at}] 类型: {memory_type}")
                                else:
                                    print(f"    ... 还有 {len(memories['metadatas']) - 5} 条记忆")
                                    break
                    except Exception as e:
                        print(f"  无法获取集合 {collection.name} 的详细信息: {e}")
            
            print()  # 空行分隔不同用户
            
        except Exception as e:
            print(f"用户ID: {user_id}")
            print(f"  最后活动时间: {last_modified_str}")
            print(f"  无法连接到数据库: {e}")
            print()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="列出所有使用教学助手服务的用户")
    parser.add_argument("-v", "--verbose", action="store_true", help="显示详细信息")
    args = parser.parse_args()
    
    list_users(args.verbose)

if __name__ == "__main__":
    main()
