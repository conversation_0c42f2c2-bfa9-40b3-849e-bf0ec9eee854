#!/usr/bin/env python3
"""
简单查看当前使用教学助手服务的用户列表

此脚本通过检查目录结构来获取当前使用服务的用户列表，不依赖 ChromaDB 库。
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

def get_memory_path():
    """获取记忆数据库路径"""
    return os.path.join(project_root, "data", "memories")

def list_users():
    """列出所有使用服务的用户"""
    memory_path = get_memory_path()
    
    if not os.path.exists(memory_path):
        print(f"记忆数据库路径不存在: {memory_path}")
        return
    
    print(f"记忆数据库路径: {memory_path}")
    
    # 获取所有用户目录
    user_dirs = []
    for item in os.listdir(memory_path):
        item_path = os.path.join(memory_path, item)
        if os.path.isdir(item_path) and not item.startswith('.'):
            user_dirs.append(item)
    
    if not user_dirs:
        print("未找到任何用户")
        return
    
    print(f"找到 {len(user_dirs)} 个用户:")
    
    # 遍历每个用户目录
    for user_id in sorted(user_dirs):
        user_path = os.path.join(memory_path, user_id)
        
        # 获取用户最后活动时间
        last_modified = datetime.fromtimestamp(os.path.getmtime(user_path))
        last_modified_str = last_modified.strftime("%Y-%m-%d %H:%M:%S")
        
        # 获取用户目录大小
        dir_size = 0
        for root, dirs, files in os.walk(user_path):
            for file in files:
                file_path = os.path.join(root, file)
                if os.path.isfile(file_path):
                    dir_size += os.path.getsize(file_path)
        
        # 转换为可读大小
        if dir_size < 1024:
            size_str = f"{dir_size} B"
        elif dir_size < 1024 * 1024:
            size_str = f"{dir_size / 1024:.2f} KB"
        else:
            size_str = f"{dir_size / (1024 * 1024):.2f} MB"
        
        # 检查是否有 chroma.sqlite3 文件
        has_db = os.path.exists(os.path.join(user_path, "chroma.sqlite3"))
        
        # 打印用户信息
        print(f"用户ID: {user_id}")
        print(f"  最后活动时间: {last_modified_str}")
        print(f"  数据库大小: {size_str}")
        print(f"  数据库文件存在: {'是' if has_db else '否'}")
        
        # 尝试查找用户信息文件
        user_info_path = os.path.join(user_path, "user_info.json")
        if os.path.exists(user_info_path):
            try:
                with open(user_info_path, 'r', encoding='utf-8') as f:
                    user_info = json.load(f)
                print(f"  用户信息: {user_info}")
            except Exception as e:
                print(f"  无法读取用户信息文件: {e}")
        
        print()  # 空行分隔不同用户

def main():
    """主函数"""
    list_users()

if __name__ == "__main__":
    main()
