#!/usr/bin/env python3
"""
查看指定用户的memory内容

此脚本允许通过用户ID查看其在ChromaDB中存储的记忆内容。
"""

import os
import sys
import json
import argparse
from datetime import datetime
import chromadb
from chromadb.config import Settings

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

def get_memory_path():
    """获取记忆数据库路径"""
    return os.path.join(project_root, "data", "memories")

def view_user_memory(user_id, verbose=False):
    """
    查看指定用户的记忆内容

    Args:
        user_id: 要查看记忆的用户ID
        verbose: 是否显示详细信息，包括记忆的文档内容
    """
    memory_path = get_memory_path()
    user_path = os.path.join(memory_path, user_id)

    if not os.path.exists(user_path) or not os.path.isdir(user_path):
        print(f"用户记忆路径不存在或不是目录: {user_path}")
        return

    print(f"正在查看用户 '{user_id}' 的记忆...")
    print(f"记忆数据库路径: {user_path}")

    try:
        client = chromadb.PersistentClient(
            path=user_path,
            settings=Settings(anonymized_telemetry=False)
        )

        collections = client.list_collections()
        if not collections:
            print(f"用户 '{user_id}' 没有记忆集合。")
            return

        print(f"找到 {len(collections)} 个记忆集合:")
        for collection in collections:
            print(f"\n--- 集合: {collection.name} ---")
            try:
                # 获取集合中的所有记忆，包括文档和元数据
                memories = collection.get(include=['documents', 'metadatas'])
                
                if not memories['ids']:
                    print("  此集合中没有记忆。")
                    continue

                print(f"  记忆数量: {len(memories['ids'])}")
                
                for i, (doc_id, metadata, document) in enumerate(zip(memories['ids'], memories['metadatas'], memories['documents'])):
                    created_at = metadata.get('created_at', 'unknown')
                    memory_type = metadata.get('memory_type', 'unknown')
                    
                    print(f"  - 记忆ID: {doc_id}")
                    print(f"    创建时间: {created_at}")
                    print(f"    类型: {memory_type}")
                    print(f"    元数据: {json.dumps(metadata, ensure_ascii=False, indent=2)}")
                    if verbose:
                        print(f"    文档内容:\n      {document}")
                    print("-" * 30) # 分隔线

            except Exception as e:
                print(f"  无法获取集合 {collection.name} 的记忆: {e}")

    except Exception as e:
        print(f"无法连接到用户 '{user_id}' 的数据库: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="查看指定用户的记忆内容")
    parser.add_argument("user_id", type=str, help="要查看记忆的用户ID")
    parser.add_argument("-v", "--verbose", action="store_true", help="显示详细的记忆文档内容")
    args = parser.parse_args()
    
    view_user_memory(args.user_id, args.verbose)

if __name__ == "__main__":
    main()