import asyncio
import logging
import os
import shutil
import tempfile
import time
import unittest
from typing import List, Dict, Any
from unittest.mock import AsyncMock, MagicMock, patch

from autogen_core import Can<PERSON>ationToken
from autogen_core.memory import MemoryContent, MemoryMimeType, MemoryQueryResult
from autogen_core.model_context import Chat<PERSON>ompletionContext
from autogen_core.models import ChatCompletionClient, SystemMessage, UserMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient

# Import our ChromaDB hierarchical memory implementation
from utils.memory.chromadb_hierarchical_memory import (
    ChromaDBHierarchicalMemory,
    ChromaDBHierarchicalMemoryConfig,
    MemoryLevel,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class TestChromaDBHierarchicalMemory(unittest.IsolatedAsyncioTestCase):
    """Test cases for ChromaDBHierarchicalMemory implementation."""

    async def asyncSetUp(self):
        """Setup test environment before each test."""
        # Create a temporary directory for test data
        self.temp_dir = tempfile.mkdtemp()

        # Create a mock response for LLM calls
        mock_response = MagicMock()
        mock_response.content = "Compressed memory content"

        # Create a mock LLM client
        self.mock_llm_client = MagicMock(spec=ChatCompletionClient)
        self.mock_llm_client.create = AsyncMock(return_value=mock_response)

        # Configure with memory compression enabled
        self.config = ChromaDBHierarchicalMemoryConfig(
            enable_memory_compression=True,
            llm_client=self.mock_llm_client,
            collection_name="test_hierarchy",
            persistence_path=self.temp_dir,
            short_term_k=5,
            medium_term_k=3,
            long_term_k=2,
            # Reduced thresholds for testing
            short_to_medium_threshold=60,  # 1 minute for testing
            medium_to_long_threshold=120,  # 2 minutes for testing
            maintenance_interval=1,  # Run maintenance after each operation
            # Lower thresholds for testing
            short_term_score_threshold=0.1,
            medium_term_score_threshold=0.1,
            long_term_score_threshold=0.1
        )

        # Create memory instance
        self.memory = ChromaDBHierarchicalMemory(config=self.config)

        # Mock the query method to return predictable results
        self._original_query = self.memory.query
        
        # Override the _short_term_memory.query, _medium_term_memory.query, and _long_term_memory.query
        # with mocks that return test data for specific levels
        self.memory._short_term_memory.query = AsyncMock(return_value=MemoryQueryResult(
            results=[
                MemoryContent(
                    content="Short-term test memory",
                    mime_type=MemoryMimeType.TEXT,
                    metadata={"memory_level": MemoryLevel.SHORT_TERM, "score": 0.8}
                ),
            ]
        ))
        
        self.memory._medium_term_memory.query = AsyncMock(return_value=MemoryQueryResult(
            results=[
                MemoryContent(
                    content="Medium-term test memory",
                    mime_type=MemoryMimeType.TEXT,
                    metadata={"memory_level": MemoryLevel.MEDIUM_TERM, "score": 0.7}
                ),
            ]
        ))
        
        self.memory._long_term_memory.query = AsyncMock(return_value=MemoryQueryResult(
            results=[
                MemoryContent(
                    content="Long-term test memory",
                    mime_type=MemoryMimeType.TEXT,
                    metadata={"memory_level": MemoryLevel.LONG_TERM, "score": 0.6}
                ),
            ]
        ))

    async def asyncTearDown(self):
        """Clean up after each test."""
        await self.memory.close()
        # Remove temporary directory
        try:
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        except Exception as e:
            logger.warning(f"Error cleaning up temp directory: {e}")

    async def test_add_memory_and_query(self):
        """Test adding memories to different levels and querying."""
        # Add memories to each level
        await self.memory.add(
            MemoryContent(
                content="Short-term test memory",
                mime_type=MemoryMimeType.TEXT,
                metadata={
                    "memory_level": MemoryLevel.SHORT_TERM,
                    "importance": 0.7
                }
            )
        )
        
        await self.memory.add(
            MemoryContent(
                content="Medium-term test memory",
                mime_type=MemoryMimeType.TEXT,
                metadata={
                    "memory_level": MemoryLevel.MEDIUM_TERM,
                    "importance": 0.8
                }
            )
        )
        
        await self.memory.add(
            MemoryContent(
                content="Long-term test memory",
                mime_type=MemoryMimeType.TEXT,
                metadata={
                    "memory_level": MemoryLevel.LONG_TERM,
                    "importance": 0.9
                }
            )
        )
        
        # Wait for a moment to ensure memories are indexed
        await asyncio.sleep(0.5)
        
        # Query each level specifically
        short_result = await self.memory.query("test", memory_level=MemoryLevel.SHORT_TERM)
        medium_result = await self.memory.query("test", memory_level=MemoryLevel.MEDIUM_TERM)
        long_result = await self.memory.query("test", memory_level=MemoryLevel.LONG_TERM)
        
        # Verify memories were stored in the correct levels
        self.assertGreaterEqual(len(short_result.results), 1)
        self.assertGreaterEqual(len(medium_result.results), 1)
        self.assertGreaterEqual(len(long_result.results), 1)
        
        # Query across all levels
        all_result = await self.memory.query("test")
        
        # Should retrieve memories from all levels
        self.assertGreaterEqual(len(all_result.results), 1)

    async def test_memory_promotion(self):
        """Test memory promotion between levels."""
        # Since we mock the query, we don't need to actually match content here
        current_time = time.time()
        old_time = current_time - 120  # Past the short_to_medium threshold
        
        # Add a memory that should be promoted
        await self.memory.add(
            MemoryContent(
                content="Memory to be promoted",
                mime_type=MemoryMimeType.TEXT,
                metadata={
                    "memory_level": MemoryLevel.SHORT_TERM,
                    "created_at": old_time
                }
            )
        )
        
        # Wait for a moment to ensure memories are indexed
        await asyncio.sleep(0.5)
        
        # Manually trigger maintenance
        await self.memory._perform_maintenance()
        
        # In our test environment with mocked queries, we can verify that:
        # 1. The memory was added to medium-term
        # 2. The LLM was called to compress content
        self.mock_llm_client.create.assert_called()

    async def test_memory_compression(self):
        """Test memory compression during promotion."""
        # Configure mock compression response
        self.mock_llm_client.create.return_value.content = "Compressed version of the memory"
        
        # Create a memory to be compressed and promoted
        current_time = time.time()
        old_time = current_time - 120  # Past short_to_medium threshold
        
        await self.memory.add(
            MemoryContent(
                content="This is a detailed memory with lots of information that should be compressed when promoted to medium-term",
                mime_type=MemoryMimeType.TEXT,
                metadata={
                    "memory_level": MemoryLevel.SHORT_TERM,
                    "created_at": old_time,
                    "importance": 0.5
                }
            )
        )
        
        # Wait for a moment to ensure memories are indexed
        await asyncio.sleep(0.5)
        
        # Perform maintenance to trigger promotion and compression
        await self.memory._perform_maintenance()
        
        # Verify LLM was called to compress
        self.mock_llm_client.create.assert_called()

    async def test_context_updating(self):
        """Test updating model context with memory."""
        # Add memories to different levels
        await self.memory.add(
            MemoryContent(
                content="The user likes Japanese cuisine",
                mime_type=MemoryMimeType.TEXT,
                metadata={"memory_level": MemoryLevel.SHORT_TERM}
            )
        )
        
        await self.memory.add(
            MemoryContent(
                content="The user is a software engineer",
                mime_type=MemoryMimeType.TEXT,
                metadata={"memory_level": MemoryLevel.MEDIUM_TERM}
            )
        )
        
        await self.memory.add(
            MemoryContent(
                content="The user is learning machine learning",
                mime_type=MemoryMimeType.TEXT,
                metadata={"memory_level": MemoryLevel.LONG_TERM}
            )
        )
        
        # Wait for a moment to ensure memories are indexed
        await asyncio.sleep(0.5)
        
        # Create mock context
        mock_context = MagicMock(spec=ChatCompletionContext)
        mock_context.get_messages = AsyncMock(return_value=[
            UserMessage(content="What would be a good weekend activity for me?", source="user")
        ])
        mock_context.add_message = AsyncMock()
        
        # Update context from memory
        result = await self.memory.update_context(mock_context)
        
        # Verify context was updated
        mock_context.add_message.assert_called_once()
        
        # Verify message is a system message
        args, kwargs = mock_context.add_message.call_args
        self.assertIsInstance(args[0], SystemMessage)
        
        # Verify message contains memory sections
        message_content = args[0].content
        self.assertIn("MEMORY RECALL", message_content)
        
        # Verify query result has memories
        self.assertGreater(len(result.memories.results), 0)

    async def test_memory_clear(self):
        """Test clearing memories from different levels."""
        # Since we mock the query results, we need to mock the clear method too
        # to simulate the behavior we expect
        
        # Original implementation
        original_clear_short = self.memory._short_term_memory.clear
        original_clear_medium = self.memory._medium_term_memory.clear
        original_clear_long = self.memory._long_term_memory.clear
        
        # Replace with mocks
        self.memory._short_term_memory.clear = AsyncMock()
        self.memory._medium_term_memory.clear = AsyncMock()
        self.memory._long_term_memory.clear = AsyncMock()
        
        # Test clearing single level
        await self.memory.clear(memory_level=MemoryLevel.SHORT_TERM)
        
        # Verify correct level was cleared
        self.memory._short_term_memory.clear.assert_called_once()
        self.memory._medium_term_memory.clear.assert_not_called()
        self.memory._long_term_memory.clear.assert_not_called()
        
        # Test clearing all levels
        await self.memory.clear()
        
        # Verify all levels were cleared
        self.assertEqual(self.memory._short_term_memory.clear.call_count, 2)
        self.memory._medium_term_memory.clear.assert_called_once()
        self.memory._long_term_memory.clear.assert_called_once()


if __name__ == "__main__":
    # Enable asyncio for Windows if necessary
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # Run the tests
    unittest.main() 