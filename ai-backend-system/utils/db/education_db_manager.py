# ai-backend-system/utils/db/education_db_manager.py
"""
教育系统数据库管理工具类。

该工具类负责管理教育系统相关的数据库操作，包括:
- 教学机构（租户）管理
- 科目管理
- 学生管理
- 报名管理
- 配额使用记录与统计

基于 DBConnector 提供高级别的数据操作接口，
处理并发访问和锁竞争问题，确保数据一致性。
"""
import os
import sqlite3  # 添加这个导入
import json
import logging
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
import random
import multiprocessing
from functools import partial


# 导入数据库连接器
from utils.db.db_connector import DBConnector, QueryError, InsertError, UpdateError, DeleteError

class EducationDBManager:
    """
    教育系统数据库管理工具类。
    
    为教育系统提供高级别的数据库操作接口，处理教学机构、科目、学生和报名等数据的
    创建、查询、更新和删除操作。同时处理并发访问和锁竞争问题，确保数据一致性。
    """
    
    def __init__(self, db_connector: DBConnector):
        """
        初始化教育系统数据库管理工具类。
        
        Args:
            db_connector (DBConnector): 数据库连接器实例
        """
        self.db = db_connector
        self.logger = logging.getLogger(__name__)
    
    # ========== 教学机构(租户)管理接口 ==========
    
    def create_institution(self, name: str, contact_person: str = None, 
                          contact_phone: str = None, contact_email: str = None,
                          max_subjects: int = 5, max_students: int = 100,
                          token_quota: int = 1000000, question_quota: int = 10000,
                          valid_until: str = None) -> Dict[str, Any]:
        """
        创建新教学机构(租户)。
        
        Args:
            name (str): 机构名称
            contact_person (str, optional): 联系人
            contact_phone (str, optional): 联系电话
            contact_email (str, optional): 联系邮箱
            max_subjects (int, optional): 最大支持科目数，默认5
            max_students (int, optional): 最大支持学生数，默认100
            token_quota (int, optional): token配额，默认1000000
            question_quota (int, optional): 提问数配额，默认10000
            valid_until (str, optional): 有效期，格式: "YYYY-MM-DD"
            
        Returns:
            Dict[str, Any]: 创建的机构信息
        """
        try:
            institution_data = {
                "name": name,
                "status": "active",
                "contact_person": contact_person,
                "contact_phone": contact_phone,
                "contact_email": contact_email,
                "max_subjects": max_subjects,
                "max_students": max_students,
                "token_quota": token_quota,
                "question_quota": question_quota
            }
            
            if valid_until:
                institution_data["valid_until"] = valid_until
            
            result = self.db.insert_data("backend_institutions", institution_data)
            self.logger.info(f"Successfully created institution: {name}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to create institution: {str(e)}")
            raise InsertError(f"Failed to create institution: {str(e)}")
    
    def get_institution(self, institution_id: int) -> Dict[str, Any]:
        """获取教学机构信息"""
        try:
            result = self.db.get_row_by_id("backend_institutions", institution_id)
            if not result:
                raise QueryError(f"Institution with ID {institution_id} not found")
            return result
        except Exception as e:
            self.logger.error(f"Failed to get institution: {str(e)}")
            raise QueryError(f"Failed to get institution: {str(e)}")
    
    def get_institutions(self, status: str = None, 
                        limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取教学机构列表"""
        try:
            query_params = {'limit': limit, 'offset': offset}
            if status:
                query_params['eq'] = {'status': status}
            return self.db.fetch_data("backend_institutions", query_params)
        except Exception as e:
            self.logger.error(f"Failed to get institutions: {str(e)}")
            raise QueryError(f"Failed to get institutions: {str(e)}")
    
    def update_institution(self, institution_id: int, 
                          update_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新教学机构信息"""
        try:
            filter_params = {'id': institution_id}
            result = self.db.update_data("backend_institutions", filter_params, update_data)
            
            self.logger.info(f"Successfully updated institution {institution_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to update institution: {str(e)}")
            raise UpdateError(f"Failed to update institution: {str(e)}")
    
    def get_institution_usage_stats(self, institution_id: int) -> Dict[str, Any]:
        """获取教学机构使用统计信息"""
        try:
            sql = """
            SELECT * FROM backend_institution_usage_stats
            WHERE id = ?
            """
            result = self.db.execute_sql(sql, (institution_id,))
            if not result:
                raise QueryError(f"Institution with ID {institution_id} not found")
            return dict(result[0])
        except Exception as e:
            self.logger.error(f"Failed to get institution usage stats: {str(e)}")
            raise QueryError(f"Failed to get institution usage stats: {str(e)}")
    
    # ========== 科目管理接口 ==========
    
    def create_subject(self, institution_id: int, name: str, 
                      max_students: int = 30, token_quota: int = 100000, 
                      question_quota: int = 1000, valid_until: str = None) -> Dict[str, Any]:
        """创建新科目"""
        try:
            # 检查机构是否存在及配额
            institution = self.get_institution(institution_id)
            if institution["current_subjects"] >= institution["max_subjects"]:
                raise InsertError(f"Institution has reached maximum subject limit: {institution['max_subjects']}")
            
            subject_data = {
                "institution_id": institution_id,
                "name": name,
                "status": "active",
                "max_students": max_students,
                "token_quota": token_quota,
                "question_quota": question_quota
            }
            
            if valid_until:
                subject_data["valid_until"] = valid_until
            else:
                subject_data["valid_until"] = institution.get("valid_until")
            
            result = self.db.insert_data("backend_subjects", subject_data)
            self.logger.info(f"Successfully created subject: {name} for institution {institution_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to create subject: {str(e)}")
            raise InsertError(f"Failed to create subject: {str(e)}")
    
    def get_subject(self, subject_id: int) -> Dict[str, Any]:
        """获取科目信息"""
        try:
            result = self.db.get_row_by_id("backend_subjects", subject_id)
            if not result:
                raise QueryError(f"Subject with ID {subject_id} not found")
            return result
        except Exception as e:
            self.logger.error(f"Failed to get subject: {str(e)}")
            raise QueryError(f"Failed to get subject: {str(e)}")
    
    def get_institution_subjects(self, institution_id: int, 
                               status: str = None) -> List[Dict[str, Any]]:
        """获取机构下的所有科目"""
        try:
            query_params = {'eq': {'institution_id': institution_id}}
            if status:
                query_params['eq']['status'] = status
            return self.db.fetch_data("backend_subjects", query_params)
        except Exception as e:
            self.logger.error(f"Failed to get institution subjects: {str(e)}")
            raise QueryError(f"Failed to get institution subjects: {str(e)}")
    
    # ========== 学生管理接口 ==========
    
    def create_student(self, name: str, wechat_id: str = None) -> Dict[str, Any]:
        """创建新学生"""
        try:
            student_data = {"name": name, "wechat_id": wechat_id}
            result = self.db.insert_data("backend_students", student_data)
            self.logger.info(f"Successfully created student: {name}")
            return result
        except Exception as e:
            self.logger.error(f"Failed to create student: {str(e)}")
            raise InsertError(f"Failed to create student: {str(e)}")
    
    def get_student(self, student_id: int = None, wechat_id: str = None) -> Dict[str, Any]:
        """获取学生信息"""
        try:
            if not student_id and not wechat_id:
                raise QueryError("Either student_id or wechat_id must be provided")
        
            query_params = {'eq': {}}
            if student_id:
                query_params['eq']['id'] = student_id
            if wechat_id:
                query_params['eq']['wechat_id'] = wechat_id
                
            result = self.db.fetch_data("backend_students", query_params)
            if not result:
                raise QueryError("Student not found with provided parameters")
            return result[0]
        except Exception as e:
            self.logger.error(f"Failed to get student: {str(e)}")
            raise QueryError(f"Failed to get student: {str(e)}")
    
    def link_student_to_institution(self, student_id: int, 
                                  institution_id: int) -> Dict[str, Any]:
        """将学生关联到教学机构"""
        try:
            # 检查学生和机构是否存在
            self.get_student(student_id)
            institution = self.get_institution(institution_id)
            
            # 检查机构学生数是否已达上限
            if institution["current_students"] >= institution["max_students"]:
                raise InsertError(f"Institution has reached maximum student limit: {institution['max_students']}")
            
            link_data = {
                "student_id": student_id,
                "institution_id": institution_id,
                "status": "active"
            }
            
            result = self.db.insert_data("backend_student_institutions", link_data)
            self.logger.info(f"Successfully linked student {student_id} to institution {institution_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to link student to institution: {str(e)}")
            raise InsertError(f"Failed to link student to institution: {str(e)}")
    
    # ========== 报名管理接口 ==========
    
    def enroll_student(self, student_id: int, subject_id: int,
                      token_quota: int = 10000, question_quota: int = 100,
                      valid_until: str = None) -> Dict[str, Any]:
        """学生报名科目"""
        try:
            # 获取科目信息和所属机构
            subject = self.get_subject(subject_id)
            institution_id = subject["institution_id"]
            
            # 检查学生是否已关联到该机构
            check_result = self.db.execute_sql(
                "SELECT COUNT(*) as count FROM backend_student_institutions WHERE student_id = ? AND institution_id = ? AND status = 'active'",
                (student_id, institution_id)
            )
            
            if check_result[0]['count'] == 0:
                # 自动关联学生到机构
                self.link_student_to_institution(student_id, institution_id)
            
            # 检查科目学生数是否已达上限
            if subject["current_students"] >= subject["max_students"]:
                raise InsertError(f"Subject has reached maximum student limit: {subject['max_students']}")
            
            enrollment_data = {
                "student_id": student_id,
                "subject_id": subject_id,
                "token_quota": token_quota,
                "question_quota": question_quota,
                "status": "active"
            }
            
            if valid_until:
                enrollment_data["valid_until"] = valid_until
            else:
                enrollment_data["valid_until"] = subject.get("valid_until")
            
            # 插入报名数据
            result = self.db.insert_data("backend_student_enrollments", enrollment_data)
            
            self.logger.info(f"Successfully enrolled student {student_id} in subject {subject_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to enroll student: {str(e)}")
            raise InsertError(f"Failed to enroll student: {str(e)}")
    
    def get_enrollment(self, student_id: int, subject_id: int) -> Dict[str, Any]:
        """获取学生在特定科目的报名信息"""
        try:
            query_params = {
                'eq': {
                    'student_id': student_id,
                    'subject_id': subject_id
                }
            }
            
            result = self.db.fetch_data("backend_student_enrollments", query_params)
            if not result:
                raise QueryError(f"Enrollment not found for student {student_id} in subject {subject_id}")
            return result[0]
        except Exception as e:
            self.logger.error(f"Failed to get enrollment: {str(e)}")
            raise QueryError(f"Failed to get enrollment: {str(e)}")
    
    # ========== 使用量统计与更新接口 ==========
    
    def increment_token_usage(self, entity_type: str, entity_id: int, 
                            amount: int = 1, 
                            performed_by: str = "system") -> Dict[str, Any]:
        """增加token使用量"""
        try:
            # 根据实体类型确定表名和字段
            if entity_type == 'institution':
                table = "backend_institutions"
            elif entity_type == 'subject':
                table = "backend_subjects"
            elif entity_type == 'enrollment':
                table = "backend_student_enrollments"
            else:
                raise ValueError(f"Invalid entity type: {entity_type}")
            
            # 确保不超过配额
            check_sql = f"SELECT token_quota, token_used FROM {table} WHERE id = ?"
            check_result = self.db.execute_sql(check_sql, (entity_id,))
            
            if not check_result:
                raise QueryError(f"{entity_type} with ID {entity_id} not found")
                
            quota = check_result[0]['token_quota']
            used = check_result[0]['token_used']
            
            if used + amount > quota:
                raise UpdateError(f"Token quota exceeded for {entity_type} {entity_id}")
            
            # 准备事务语句
            sql_statements = []
            
            # 更新当前实体的使用量
            update_sql = f"""
            UPDATE {table}
            SET token_used = token_used + ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            sql_statements.append((update_sql, (amount, entity_id)))
            
            # 如果是科目或报名，更新上层实体
            if entity_type == 'subject':
                # 获取机构ID
                inst_sql = "SELECT institution_id FROM backend_subjects WHERE id = ?"
                inst_result = self.db.execute_sql(inst_sql, (entity_id,))
                
                if inst_result:
                    inst_id = inst_result[0]['institution_id']
                    # 更新机构使用量
                    update_inst_sql = """
                    UPDATE backend_institutions
                    SET token_used = token_used + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                    """
                    sql_statements.append((update_inst_sql, (amount, inst_id)))
                    
            elif entity_type == 'enrollment':
                # 获取科目ID和关联的机构ID
                subj_sql = "SELECT subject_id FROM backend_student_enrollments WHERE id = ?"
                subj_result = self.db.execute_sql(subj_sql, (entity_id,))
                
                if subj_result:
                    subj_id = subj_result[0]['subject_id']
                    
                    # 更新科目使用量
                    update_subj_sql = """
                    UPDATE backend_subjects
                    SET token_used = token_used + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                    """
                    sql_statements.append((update_subj_sql, (amount, subj_id)))
                    
                    # 获取机构ID
                    inst_sql = "SELECT institution_id FROM backend_subjects WHERE id = ?"
                    inst_result = self.db.execute_sql(inst_sql, (subj_id,))
                    
                    if inst_result:
                        inst_id = inst_result[0]['institution_id']
                        
                        # 更新机构使用量
                        update_inst_sql = """
                        UPDATE backend_institutions
                        SET token_used = token_used + ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                        """
                        sql_statements.append((update_inst_sql, (amount, inst_id)))
            
            # 添加日志记录
            log_sql = """
            INSERT INTO backend_education_logs
            (action, entity_type, entity_id, change_amount, previous_value, new_value, performed_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            log_params = (
                'increment_token',
                entity_type,
                entity_id,
                amount,
                used,
                used + amount,
                performed_by
            )
            sql_statements.append((log_sql, log_params))
            
            # 执行事务
            self.db.execute_transaction(sql_statements)
            
            self.logger.info(f"Successfully incremented token usage for {entity_type} {entity_id} by {amount}")
            return {"success": True, "message": f"Token usage incremented by {amount}"}
                
        except Exception as e:
            self.logger.error(f"Failed to increment token usage: {str(e)}")
            raise UpdateError(f"Failed to increment token usage: {str(e)}")
        
    def increment_question_count(self, entity_type: str, entity_id: int, 
                               amount: int = 1, 
                               performed_by: str = "system") -> Dict[str, Any]:
        """增加问题使用数量"""
        try:
            # 根据实体类型确定表名和字段
            if entity_type == 'institution':
                table = "backend_institutions"
                count_field = "questions_asked"
                quota_field = "question_quota"
            elif entity_type == 'subject':
                table = "backend_subjects"
                count_field = "questions_asked"
                quota_field = "question_quota"
            elif entity_type == 'enrollment':
                table = "backend_student_enrollments"
                count_field = "questions_asked"
                quota_field = "question_quota"
            else:
                raise ValueError(f"Invalid entity type: {entity_type}")
            
            # 获取当前使用量和配额
            check_sql = f"SELECT {quota_field}, {count_field} FROM {table} WHERE id = ?"
            check_result = self.db.execute_sql(check_sql, (entity_id,))
            
            if not check_result:
                raise QueryError(f"{entity_type} with ID {entity_id} not found")
                
            quota = check_result[0][quota_field]
            used = check_result[0][count_field]
            
            if used + amount > quota:
                raise UpdateError(f"Question quota exceeded for {entity_type} {entity_id}")
            
            # 准备事务语句
            sql_statements = []
            
            # 更新当前实体的使用量
            update_sql = f"""
            UPDATE {table}
            SET {count_field} = {count_field} + ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            sql_statements.append((update_sql, (amount, entity_id)))
            
            # 如果是科目或报名，更新上层实体
            if entity_type == 'subject':
                # 获取机构ID
                inst_sql = "SELECT institution_id FROM backend_subjects WHERE id = ?"
                inst_result = self.db.execute_sql(inst_sql, (entity_id,))
                
                if inst_result:
                    inst_id = inst_result[0]['institution_id']
                    # 更新机构使用量
                    update_inst_sql = """
                    UPDATE backend_institutions
                    SET questions_asked = questions_asked + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                    """
                    sql_statements.append((update_inst_sql, (amount, inst_id)))
                    
            elif entity_type == 'enrollment':
                # 获取科目ID和关联的机构ID
                subj_sql = "SELECT subject_id FROM backend_student_enrollments WHERE id = ?"
                subj_result = self.db.execute_sql(subj_sql, (entity_id,))
                
                if subj_result:
                    subj_id = subj_result[0]['subject_id']
                    
                    # 更新科目使用量
                    update_subj_sql = """
                    UPDATE backend_subjects
                    SET questions_asked = questions_asked + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                    """
                    sql_statements.append((update_subj_sql, (amount, subj_id)))
                    
                    # 获取机构ID
                    inst_sql = "SELECT institution_id FROM backend_subjects WHERE id = ?"
                    inst_result = self.db.execute_sql(inst_sql, (subj_id,))
                    
                    if inst_result:
                        inst_id = inst_result[0]['institution_id']
                        
                        # 更新机构使用量
                        update_inst_sql = """
                        UPDATE backend_institutions
                        SET questions_asked = questions_asked + ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                        """
                        sql_statements.append((update_inst_sql, (amount, inst_id)))
            
            # 添加日志记录
            log_sql = """
            INSERT INTO backend_education_logs
            (action, entity_type, entity_id, change_amount, previous_value, new_value, performed_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            log_params = (
                'increment_question',
                entity_type,
                entity_id,
                amount,
                used,
                used + amount,
                performed_by
            )
            sql_statements.append((log_sql, log_params))
            
            # 执行事务
            self.db.execute_transaction(sql_statements)
            
            self.logger.info(f"Successfully incremented question count for {entity_type} {entity_id} by {amount}")
            return {"success": True, "message": f"Question count incremented by {amount}"}
                
        except Exception as e:
            self.logger.error(f"Failed to increment question count: {str(e)}")
            raise UpdateError(f"Failed to increment question count: {str(e)}")
    
    def get_student_activity(self, student_id: int = None, 
                           institution_id: int = None) -> List[Dict[str, Any]]:
        """获取学生活动情况"""
        try:
            if student_id and institution_id:
                sql = """
                SELECT * FROM backend_student_activity
                WHERE student_id = ? AND institution_id = ?
                """
                return self.db.execute_sql(sql, (student_id, institution_id))
                
            elif student_id:
                sql = """
                SELECT * FROM backend_student_activity
                WHERE student_id = ?
                """
                return self.db.execute_sql(sql, (student_id,))
                
            elif institution_id:
                sql = """
                SELECT * FROM backend_student_activity
                WHERE institution_id = ?
                """
                return self.db.execute_sql(sql, (institution_id,))
                
            else:
                raise QueryError("Either student_id or institution_id must be provided")
                
        except Exception as e:
            self.logger.error(f"Failed to get student activity: {str(e)}")
            raise QueryError(f"Failed to get student activity: {str(e)}")
    
    def get_usage_logs(self, entity_type: str = None, entity_id: int = None,
                     action: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取使用日志"""
        try:
            conditions = []
            params = []
            
            if entity_type:
                conditions.append("entity_type = ?")
                params.append(entity_type)
                
            if entity_id:
                conditions.append("entity_id = ?")
                params.append(entity_id)
                
            if action:
                conditions.append("action = ?")
                params.append(action)
                
            sql = "SELECT * FROM backend_education_logs"
            
            if conditions:
                sql += " WHERE " + " AND ".join(conditions)
                
            sql += f" ORDER BY performed_at DESC LIMIT {limit}"
            
            return self.db.execute_sql(sql, tuple(params))
            
        except Exception as e:
            self.logger.error(f"Failed to get usage logs: {str(e)}")
            raise QueryError(f"Failed to get usage logs: {str(e)}")
    
    # ========== 高级查询接口 ==========
    
    def get_expiring_entities(self, entity_type: str, days_threshold: int = 30) -> List[Dict[str, Any]]:
        """
        获取即将过期的实体（机构、科目、报名）。
        
        Args:
            entity_type (str): 实体类型，'institution', 'subject', 或 'enrollment'
            days_threshold (int): 过期天数阈值，默认30天内
            
        Returns:
            List[Dict[str, Any]]: 即将过期的实体列表
        """
        try:
            future_date = datetime.now() + timedelta(days=days_threshold)
            future_date_str = future_date.strftime('%Y-%m-%d')
            
            if entity_type == 'institution':
                sql = """
                SELECT * FROM backend_institutions
                WHERE valid_until IS NOT NULL 
                AND valid_until <= ?
                AND valid_until >= date('now')
                AND status = 'active'
                ORDER BY valid_until
                """
            elif entity_type == 'subject':
                sql = """
                SELECT s.*, i.name as institution_name
                FROM backend_subjects s
                JOIN backend_institutions i ON s.institution_id = i.id
                WHERE s.valid_until IS NOT NULL 
                AND s.valid_until <= ?
                AND s.valid_until >= date('now')
                AND s.status = 'active'
                ORDER BY s.valid_until
                """
            elif entity_type == 'enrollment':
                sql = """
                SELECT e.*, s.name as subject_name, st.name as student_name,
                       i.name as institution_name
                FROM backend_student_enrollments e
                JOIN backend_subjects s ON e.subject_id = s.id
                JOIN backend_students st ON e.student_id = st.id
                JOIN backend_institutions i ON s.institution_id = i.id
                WHERE e.valid_until IS NOT NULL 
                AND e.valid_until <= ?
                AND e.valid_until >= date('now')
                AND e.status = 'active'
                ORDER BY e.valid_until
                """
            else:
                raise ValueError(f"Invalid entity type: {entity_type}")
                
            return self.db.execute_sql(sql, (future_date_str,))
            
        except Exception as e:
            self.logger.error(f"Failed to get expiring {entity_type}s: {str(e)}")
            raise QueryError(f"Failed to get expiring {entity_type}s: {str(e)}")
    
    def get_high_usage_entities(self, entity_type: str, usage_type: str = "token", 
                               percentage_threshold: float = 80.0) -> List[Dict[str, Any]]:
        """
        获取高使用量的实体（机构、科目、报名）。
        
        Args:
            entity_type (str): 实体类型，'institution', 'subject', 或 'enrollment'
            usage_type (str): 使用类型，'token' 或 'question'
            percentage_threshold (float): 使用百分比阈值，默认80%
            
        Returns:
            List[Dict[str, Any]]: 高使用量的实体列表
        """
        try:
            if usage_type == "token":
                quota_field = "token_quota"
                used_field = "token_used"
            elif usage_type == "question":
                quota_field = "question_quota"
                used_field = "questions_asked"
            else:
                raise ValueError(f"Invalid usage type: {usage_type}")
                
            if entity_type == 'institution':
                sql = f"""
                SELECT *, (CAST({used_field} AS FLOAT) / {quota_field} * 100) as usage_percentage
                FROM backend_institutions
                WHERE {quota_field} > 0
                AND (CAST({used_field} AS FLOAT) / {quota_field} * 100) >= ?
                AND status = 'active'
                ORDER BY usage_percentage DESC
                """
            elif entity_type == 'subject':
                sql = f"""
                SELECT s.*, i.name as institution_name,
                       (CAST(s.{used_field} AS FLOAT) / s.{quota_field} * 100) as usage_percentage
                FROM backend_subjects s
                JOIN backend_institutions i ON s.institution_id = i.id
                WHERE s.{quota_field} > 0
                AND (CAST(s.{used_field} AS FLOAT) / s.{quota_field} * 100) >= ?
                AND s.status = 'active'
                ORDER BY usage_percentage DESC
                """
            elif entity_type == 'enrollment':
                sql = f"""
                SELECT e.*, s.name as subject_name, st.name as student_name,
                       i.name as institution_name,
                       (CAST(e.{used_field} AS FLOAT) / e.{quota_field} * 100) as usage_percentage
                FROM backend_student_enrollments e
                JOIN backend_subjects s ON e.subject_id = s.id
                JOIN backend_students st ON e.student_id = st.id
                JOIN backend_institutions i ON s.institution_id = i.id
                WHERE e.{quota_field} > 0
                AND (CAST(e.{used_field} AS FLOAT) / e.{quota_field} * 100) >= ?
                AND e.status = 'active'
                ORDER BY usage_percentage DESC
                """
            else:
                raise ValueError(f"Invalid entity type: {entity_type}")
                
            return self.db.execute_sql(sql, (percentage_threshold,))
            
        except Exception as e:
            self.logger.error(f"Failed to get high usage {entity_type}s: {str(e)}")
            raise QueryError(f"Failed to get high usage {entity_type}s: {str(e)}")
    
    def search_students(self, search_term: str, institution_id: int = None) -> List[Dict[str, Any]]:
        """
        搜索学生。
        
        Args:
            search_term (str): 搜索条件（姓名或微信ID）
            institution_id (int, optional): 机构ID限制
            
        Returns:
            List[Dict[str, Any]]: 符合条件的学生列表
        """
        try:
            search_term = f"%{search_term}%"
            
            if institution_id:
                sql = """
                SELECT DISTINCT s.* 
                FROM backend_students s
                JOIN backend_student_institutions si ON s.id = si.student_id
                WHERE (s.name LIKE ? OR s.wechat_id LIKE ?)
                AND si.institution_id = ?
                AND si.status = 'active'
                ORDER BY s.name
                """
                return self.db.execute_sql(sql, (search_term, search_term, institution_id))
            else:
                sql = """
                SELECT * FROM backend_students
                WHERE name LIKE ? OR wechat_id LIKE ?
                ORDER BY name
                """
                return self.db.execute_sql(sql, (search_term, search_term))
                
        except Exception as e:
            self.logger.error(f"Failed to search students: {str(e)}")
            raise QueryError(f"Failed to search students: {str(e)}")
    
    # ========== 批量操作接口 ==========
    
    def batch_update_enrollment_status(self, enrollment_ids: List[int], 
                                     new_status: str) -> Dict[str, Any]:
        """
        批量更新报名状态。
        
        Args:
            enrollment_ids (List[int]): 报名ID列表
            new_status (str): 新状态，'active', 'suspended', 或 'expired'
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            if not enrollment_ids:
                return {"success": True, "affected_rows": 0}
                
            # 开始事务
            sql_begin = "BEGIN;"
            self.db.execute_sql(sql_begin)
            
            # 使用参数化查询防止SQL注入
            placeholders = ','.join(['?' for _ in enrollment_ids])
            sql = f"""
            UPDATE backend_student_enrollments
            SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id IN ({placeholders})
            """
            
            params = [new_status] + enrollment_ids
            self.db.execute_sql(sql, params)
            
            # 提交事务
            sql_commit = "COMMIT;"
            self.db.execute_sql(sql_commit)
            
            self.logger.info(f"Successfully updated {len(enrollment_ids)} enrollments to status '{new_status}'")
            return {"success": True, "affected_rows": len(enrollment_ids)}
            
        except Exception as e:
            # 回滚事务
            sql_rollback = "ROLLBACK;"
            self.db.execute_sql(sql_rollback)
            
            self.logger.error(f"Failed to batch update enrollment status: {str(e)}")
            raise UpdateError(f"Failed to batch update enrollment status: {str(e)}")
    
    def batch_add_subjects_quota(self, subject_ids: List[int], 
                               token_amount: int = 0, 
                               question_amount: int = 0) -> Dict[str, Any]:
        """
        批量增加科目配额。
        
        Args:
            subject_ids (List[int]): 科目ID列表
            token_amount (int): 要增加的token数量
            question_amount (int): 要增加的问题数量
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            if not subject_ids or (token_amount == 0 and question_amount == 0):
                return {"success": True, "affected_rows": 0}
                
            # 开始事务
            sql_begin = "BEGIN;"
            self.db.execute_sql(sql_begin)
            
            # 构建更新SQL
            update_clauses = []
            params = []
            
            if token_amount != 0:
                update_clauses.append("token_quota = token_quota + ?")
                params.append(token_amount)
                
            if question_amount != 0:
                update_clauses.append("question_quota = question_quota + ?")
                params.append(question_amount)
                
            update_clauses.append("updated_at = CURRENT_TIMESTAMP")
            
            # 使用参数化查询防止SQL注入
            placeholders = ','.join(['?' for _ in subject_ids])
            params.extend(subject_ids)
            
            sql = f"""
            UPDATE backend_subjects
            SET {', '.join(update_clauses)}
            WHERE id IN ({placeholders})
            """
            
            self.db.execute_sql(sql, params)
            
            # 提交事务
            sql_commit = "COMMIT;"
            self.db.execute_sql(sql_commit)
            
            self.logger.info(f"Successfully added quota to {len(subject_ids)} subjects")
            return {"success": True, "affected_rows": len(subject_ids)}
            
        except Exception as e:
            # 回滚事务
            sql_rollback = "ROLLBACK;"
            self.db.execute_sql(sql_rollback)
            
            self.logger.error(f"Failed to batch add subjects quota: {str(e)}")
            raise UpdateError(f"Failed to batch add subjects quota: {str(e)}")
    
    # ========== 报告和导出接口 ==========
    
    def generate_institution_report(self, institution_id: int, as_markdown: bool = False) -> str:
        """
        生成教学机构使用报告。
        
        Args:
            institution_id (int): 机构ID
            as_markdown (bool, optional): 是否以Markdown格式返回。默认为False。
            
        Returns:
            str: 生成的报告
        """
        try:
            # 获取机构信息和统计数据
            stats = self.get_institution_usage_stats(institution_id)
            
            # 获取科目列表
            subjects = self.get_institution_subjects(institution_id)
            
            # 计算学生数量
            student_count_sql = """
            SELECT COUNT(DISTINCT student_id) as count
            FROM backend_student_institutions
            WHERE institution_id = ? AND status = 'active'
            """
            student_count_result = self.db.execute_sql(student_count_sql, (institution_id,))
            student_count = student_count_result[0]['count'] if student_count_result else 0
            
            # 计算总登录数和平均每天登录数 (这部分可能需要额外的表，这里用模拟数据)
            logins_count = 0
            avg_daily_logins = 0
            
            # 生成报告
            if as_markdown:
                report = f"# {stats['name']} 使用报告\n\n"
                
                # 基本信息
                report += "## 基本信息\n\n"
                report += f"- **名称**: {stats['name']}\n"
                report += f"- **联系人**: {stats['contact_person'] or '未设置'}\n"
                report += f"- **联系邮箱**: {stats['contact_email'] or '未设置'}\n"
                report += f"- **状态**: {stats['status']}\n"
                report += f"- **有效期至**: {stats['valid_until'] or '永久'}\n\n"
                
                # 使用统计
                report += "## 使用统计\n\n"
                report += f"- **科目数**: {stats['current_subjects']}/{stats['max_subjects']}\n"
                report += f"- **学生数**: {stats['current_students']}/{stats['max_students']}\n"
                report += f"- **Token使用**: {stats['token_used']}/{stats['token_quota']} ({(stats['token_used']/stats['token_quota']*100):.1f}%)\n"
                report += f"- **问题数使用**: {stats['questions_asked']}/{stats['question_quota']} ({(stats['questions_asked']/stats['question_quota']*100):.1f}%)\n\n"
                
                # 科目列表
                report += "## 科目列表\n\n"
                report += "| ID | 科目名称 | 学生数 | Token使用 | 问题数使用 | 状态 |\n"
                report += "|---|---------|--------|-----------|------------|------|\n"
                
                for subject in subjects:
                    report += f"| {subject['id']} | {subject['name']} | {subject['current_students']}/{subject['max_students']} | "
                    report += f"{subject['token_used']}/{subject['token_quota']} | {subject['questions_asked']}/{subject['question_quota']} | {subject['status']} |\n"
                
                report += "\n"
                
                # 使用趋势 (这部分可能需要额外的表，这里用静态文本代替)
                report += "## 使用趋势\n\n"
                report += "使用趋势数据需要额外的日志分析，暂未实现。\n\n"
                
                # 推荐
                report += "## 建议和推荐\n\n"
                
                # 根据使用情况给出建议
                token_usage_percent = stats['token_used'] / stats['token_quota'] * 100
                question_usage_percent = stats['questions_asked'] / stats['question_quota'] * 100
                
                if token_usage_percent > 80:
                    report += "- **注意**: Token使用量已超过配额的80%，建议增加配额或控制使用频率。\n"
                    
                if question_usage_percent > 80:
                    report += "- **注意**: 问题使用量已超过配额的80%，建议增加配额或控制使用频率。\n"
                    
                if stats['current_subjects'] >= stats['max_subjects']:
                    report += "- **注意**: 已达到最大科目数限制，无法添加更多科目。\n"
                    
                if stats['current_students'] >= stats['max_students'] * 0.9:
                    report += "- **注意**: 学生数接近最大限制，建议增加学生配额。\n"
                    
                if not stats['contact_email'] and not stats['contact_person']:
                    report += "- **建议**: 添加联系人信息，以便更好地沟通。\n"
                
                # 如果没有任何建议，给出积极评价
                if "**注意**" not in report and "**建议**" not in report:
                    report += "- 当前使用状况良好，无特别建议。\n"
                
            else:
                # 文本格式报告
                report = f"{stats['name']}使用报告\n"
                report += "=" * 50 + "\n\n"
                
                # 基本信息
                report += "基本信息:\n"
                report += f"名称: {stats['name']}\n"
                report += f"联系人: {stats['contact_person'] or '未设置'}\n"
                report += f"联系邮箱: {stats['contact_email'] or '未设置'}\n"
                report += f"状态: {stats['status']}\n"
                report += f"有效期至: {stats['valid_until'] or '永久'}\n\n"
                
                # 使用统计
                report += "使用统计:\n"
                report += f"科目数: {stats['current_subjects']}/{stats['max_subjects']}\n"
                report += f"学生数: {stats['current_students']}/{stats['max_students']}\n"
                report += f"Token使用: {stats['token_used']}/{stats['token_quota']} ({(stats['token_used']/stats['token_quota']*100):.1f}%)\n"
                report += f"问题数使用: {stats['questions_asked']}/{stats['question_quota']} ({(stats['questions_asked']/stats['question_quota']*100):.1f}%)\n\n"
                
                # 科目列表
                report += "科目列表:\n"
                for subject in subjects:
                    report += f"- {subject['name']} (ID: {subject['id']}): "
                    report += f"学生数 {subject['current_students']}/{subject['max_students']}, "
                    report += f"Token使用 {subject['token_used']}/{subject['token_quota']}, "
                    report += f"问题数使用 {subject['questions_asked']}/{subject['question_quota']}, "
                    report += f"状态: {subject['status']}\n"
                
                report += "\n"
                
                # 使用趋势
                report += "使用趋势:\n"
                report += "使用趋势数据需要额外的日志分析，暂未实现。\n\n"
                
                # 推荐
                report += "建议和推荐:\n"
                
                # 根据使用情况给出建议
                token_usage_percent = stats['token_used'] / stats['token_quota'] * 100
                question_usage_percent = stats['questions_asked'] / stats['question_quota'] * 100
                
                if token_usage_percent > 80:
                    report += "- 注意: Token使用量已超过配额的80%，建议增加配额或控制使用频率。\n"
                    
                if question_usage_percent > 80:
                    report += "- 注意: 问题使用量已超过配额的80%，建议增加配额或控制使用频率。\n"
                    
                if stats['current_subjects'] >= stats['max_subjects']:
                    report += "- 注意: 已达到最大科目数限制，无法添加更多科目。\n"
                    
                if stats['current_students'] >= stats['max_students'] * 0.9:
                    report += "- 注意: 学生数接近最大限制，建议增加学生配额。\n"
                    
                if not stats['contact_email'] and not stats['contact_person']:
                    report += "- 建议: 添加联系人信息，以便更好地沟通。\n"
                
                # 如果没有任何建议，给出积极评价
                if "注意:" not in report and "建议:" not in report:
                    report += "- 当前使用状况良好，无特别建议。\n"
            
            self.logger.info(f"Successfully generated report for institution {institution_id}")
            return report
                
        except Exception as e:
            self.logger.error(f"Failed to generate institution report: {str(e)}")
            raise QueryError(f"Failed to generate institution report: {str(e)}")
    
    def export_student_data(self, institution_id: int = None, 
                           as_csv: bool = False) -> Union[str, List[Dict[str, Any]]]:
        """
        导出学生数据。
        
        Args:
            institution_id (int, optional): 机构ID限制
            as_csv (bool): 是否返回CSV格式
            
        Returns:
            Union[str, List[Dict[str, Any]]]: CSV格式的数据或数据列表
        """
        try:
            if institution_id:
                sql = """
                SELECT s.id, s.name, s.wechat_id, s.created_at,
                       COUNT(DISTINCT e.id) as enrollment_count
                FROM backend_students s
                JOIN backend_student_institutions si ON s.id = si.student_id
                LEFT JOIN backend_student_enrollments e ON s.id = e.student_id
                WHERE si.institution_id = ? AND si.status = 'active'
                GROUP BY s.id
                ORDER BY s.name
                """
                students = self.db.execute_sql(sql, (institution_id,))
            else:
                sql = """
                SELECT s.id, s.name, s.wechat_id, s.created_at,
                       COUNT(DISTINCT e.id) as enrollment_count
                FROM backend_students s
                LEFT JOIN backend_student_enrollments e ON s.id = e.student_id
                GROUP BY s.id
                ORDER BY s.name
                """
                students = self.db.execute_sql(sql)
                
            if not as_csv:
                return students
                
            # 生成CSV
            if not students:
                return "id,name,wechat_id,created_at,enrollment_count\n"
                
            csv_data = "id,name,wechat_id,created_at,enrollment_count\n"
            for student in students:
                # 处理CSV中的特殊字符
                name = f'"{student["name"]}"' if ',' in student["name"] else student["name"]
                wechat_id = f'"{student["wechat_id"]}"' if student["wechat_id"] and ',' in student["wechat_id"] else student["wechat_id"] or ""
                
                csv_data += f"{student['id']},{name},{wechat_id},{student['created_at']},{student['enrollment_count']}\n"
                
            return csv_data
            
        except Exception as e:
            self.logger.error(f"Failed to export student data: {str(e)}")
            raise QueryError(f"Failed to export student data: {str(e)}")
    
    # ========== 配额管理接口 ==========
    
    def adjust_quota(self, entity_type: str, entity_id: int, 
                    quota_type: str, new_quota: int) -> Dict[str, Any]:
        """
        调整实体的配额限制。
        
        Args:
            entity_type (str): 实体类型，'institution', 'subject', 或 'enrollment'
            entity_id (int): 实体ID
            quota_type (str): 配额类型，'token' 或 'question'
            new_quota (int): 新的配额值
            
        Returns:
            Dict[str, Any]: 调整结果，包含previous_quota和new_quota
        """
        try:
            # 确定表名、配额字段和使用量字段
            if entity_type == 'institution':
                table = "backend_institutions"
            elif entity_type == 'subject':
                table = "backend_subjects"
            elif entity_type == 'enrollment':
                table = "backend_student_enrollments"
            else:
                raise ValueError(f"Invalid entity type: {entity_type}")
                
            if quota_type == 'token':
                quota_field = "token_quota"
                used_field = "token_used"  # 这个字段名称是正确的
            elif quota_type == 'question':
                quota_field = "question_quota"
                used_field = "questions_asked"  # 修复：使用正确的字段名 questions_asked 而不是 question_used
            else:
                raise ValueError(f"Invalid quota type: {quota_type}")
            
            # 获取当前配额和使用量
            check_sql = f"SELECT {quota_field}, {used_field} FROM {table} WHERE id = ?"
            result = self.db.execute_sql(check_sql, (entity_id,))
            
            if not result:
                raise QueryError(f"{entity_type} with ID {entity_id} not found")
                
            current_quota = result[0][quota_field]
            current_used = result[0][used_field]
            
            # 确保新配额不低于当前使用量
            if new_quota < current_used:
                raise UpdateError(f"New quota {new_quota} cannot be less than current usage {current_used}")
            
            # 更新配额
            update_sql = f"""
            UPDATE {table}
            SET {quota_field} = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            self.db.execute_sql(update_sql, (new_quota, entity_id))
            
            # 记录操作
            log_sql = """
            INSERT INTO backend_education_logs
            (action, entity_type, entity_id, change_amount, previous_value, new_value, performed_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            log_params = (
                f'adjust_{quota_type}_quota',
                entity_type,
                entity_id,
                new_quota - current_quota,
                current_quota,
                new_quota,
                "system"
            )
            self.db.execute_sql(log_sql, log_params)
            
            self.logger.info(f"Successfully adjusted {quota_type} quota for {entity_type} {entity_id} to {new_quota}")
            return {
                "success": True,
                "previous_quota": current_quota,
                "new_quota": new_quota,
                "entity_type": entity_type,
                "entity_id": entity_id,
                "quota_type": quota_type
            }
                
        except Exception as e:
            self.logger.error(f"Failed to adjust quota: {str(e)}")
            raise UpdateError(f"Failed to adjust quota: {str(e)}")
    
    def transfer_quota(self, from_entity_type: str, from_entity_id: int,
                      to_entity_type: str, to_entity_id: int,
                      quota_type: str, amount: int) -> Dict[str, Any]:
        """
        转移配额。
        
        Args:
            from_entity_type (str): 源实体类型
            from_entity_id (int): 源实体ID
            to_entity_type (str): 目标实体类型
            to_entity_id (int): 目标实体ID
            quota_type (str): 配额类型，'token' 或 'question'
            amount (int): 转移数量
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            if quota_type not in ('token', 'question'):
                raise ValueError(f"Invalid quota type: {quota_type}")
                
            quota_field = f"{quota_type}_quota"
            used_field = f"{quota_type}_used"
            
            # 定义表名映射
            table_map = {
                'institution': "backend_institutions",
                'subject': "backend_subjects",
                'enrollment': "backend_student_enrollments"
            }
            
            if from_entity_type not in table_map or to_entity_type not in table_map:
                raise ValueError(f"Invalid entity type: {from_entity_type} or {to_entity_type}")
                
            from_table = table_map[from_entity_type]
            to_table = table_map[to_entity_type]
            
            # 开始事务
            sql_begin = "BEGIN;"
            self.db.execute_sql(sql_begin)
            
            # 检查源实体
            sql_check_from = f"""
            SELECT {quota_field}, {used_field} FROM {from_table}
            WHERE id = ?
            """
            
            from_result = self.db.execute_sql(sql_check_from, (from_entity_id,))
            
            if not from_result:
                raise QueryError(f"{from_entity_type.capitalize()} with ID {from_entity_id} not found")
                
            from_quota = from_result[0][quota_field]
            from_used = from_result[0][used_field]
            
            # 检查源实体是否有足够的可用配额
            available_quota = from_quota - from_used
            
            if available_quota < amount:
                raise UpdateError(f"Insufficient available quota: {available_quota} < {amount}")
                
            # 检查目标实体
            sql_check_to = f"""
            SELECT {quota_field} FROM {to_table}
            WHERE id = ?
            """
            
            to_result = self.db.execute_sql(sql_check_to, (to_entity_id,))
            
            if not to_result:
                raise QueryError(f"{to_entity_type.capitalize()} with ID {to_entity_id} not found")
                
            to_quota = to_result[0][quota_field]
            
            # 更新源实体配额
            sql_update_from = f"""
            UPDATE {from_table}
            SET {quota_field} = {quota_field} - ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            
            self.db.execute_sql(sql_update_from, (amount, from_entity_id))
            
            # 更新目标实体配额
            sql_update_to = f"""
            UPDATE {to_table}
            SET {quota_field} = {quota_field} + ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            
            self.db.execute_sql(sql_update_to, (amount, to_entity_id))
            
            # 记录转移日志
            sql_log = """
            INSERT INTO backend_education_logs
            (action, entity_type, entity_id, change_amount, previous_value, new_value, performed_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            # 源实体日志
            self.db.execute_sql(sql_log, (
                f"transfer_{quota_type}_out",
                from_entity_type,
                from_entity_id,
                -amount,
                from_quota,
                from_quota - amount,
                "system"
            ))
            
            # 目标实体日志
            self.db.execute_sql(sql_log, (
                f"transfer_{quota_type}_in",
                to_entity_type,
                to_entity_id,
                amount,
                to_quota,
                to_quota + amount,
                "system"
            ))
            
            # 提交事务
            sql_commit = "COMMIT;"
            self.db.execute_sql(sql_commit)
            
            self.logger.info(f"Successfully transferred {amount} {quota_type} quota from {from_entity_type} {from_entity_id} to {to_entity_type} {to_entity_id}")
            return {
                "success": True,
                "message": f"Successfully transferred {amount} {quota_type} quota",
                "from_previous_quota": from_quota,
                "from_new_quota": from_quota - amount,
                "to_previous_quota": to_quota,
                "to_new_quota": to_quota + amount
            }
            
        except Exception as e:
            # 回滚事务
            sql_rollback = "ROLLBACK;"
            self.db.execute_sql(sql_rollback)
            
            self.logger.error(f"Failed to transfer quota: {str(e)}")
            if isinstance(e, (QueryError, UpdateError, ValueError)):
                raise
            raise UpdateError(f"Failed to transfer quota: {str(e)}")
    
    # ========== 数据维护接口 ==========
    
    def update_expired_status(self) -> Dict[str, Any]:
        """
        更新过期实体的状态。
        
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            # 开始事务
            sql_begin = "BEGIN;"
            self.db.execute_sql(sql_begin)
            
            # 更新机构状态
            sql_institutions = """
            UPDATE backend_institutions
            SET status = 'expired', updated_at = CURRENT_TIMESTAMP
            WHERE valid_until IS NOT NULL
            AND valid_until < date('now')
            AND status = 'active'
            """
            
            institution_result = self.db.execute_sql(sql_institutions)
            
            # 更新科目状态
            sql_subjects = """
            UPDATE backend_subjects
            SET status = 'expired', updated_at = CURRENT_TIMESTAMP
            WHERE valid_until IS NOT NULL
            AND valid_until < date('now')
            AND status = 'active'
            """
            
            subject_result = self.db.execute_sql(sql_subjects)
            
            # 更新报名状态
            sql_enrollments = """
            UPDATE backend_student_enrollments
            SET status = 'expired', updated_at = CURRENT_TIMESTAMP
            WHERE valid_until IS NOT NULL
            AND valid_until < date('now')
            AND status = 'active'
            """
            
            enrollment_result = self.db.execute_sql(sql_enrollments)
            
            # 提交事务
            sql_commit = "COMMIT;"
            self.db.execute_sql(sql_commit)
            
            self.logger.info("Successfully updated expired status")
            return {
                "success": True,
                "institutions_updated": institution_result.rowcount if hasattr(institution_result, 'rowcount') else 0,
                "subjects_updated": subject_result.rowcount if hasattr(subject_result, 'rowcount') else 0,
                "enrollments_updated": enrollment_result.rowcount if hasattr(enrollment_result, 'rowcount') else 0
            }
            
        except Exception as e:
            # 回滚事务
            sql_rollback = "ROLLBACK;"
            self.db.execute_sql(sql_rollback)
            
            self.logger.error(f"Failed to update expired status: {str(e)}")
            raise UpdateError(f"Failed to update expired status: {str(e)}")
    
    def archive_logs(self, days_old: int = 90) -> Dict[str, Any]:
        """
        归档旧日志。
        
        Args:
            days_old (int): 要归档的日志天数
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            # 计算截止日期
            cutoff_date = datetime.now() - timedelta(days=days_old)
            cutoff_date_str = cutoff_date.strftime('%Y-%m-%d %H:%M:%S')
            
            # 创建归档表（如果不存在）
            sql_create_archive = """
            CREATE TABLE IF NOT EXISTS backend_education_logs_archive (
                id INTEGER PRIMARY KEY,
                action VARCHAR(50) NOT NULL,
                entity_type VARCHAR(50) NOT NULL,
                entity_id INTEGER NOT NULL,
                change_amount INTEGER NOT NULL,
                previous_value INTEGER,
                new_value INTEGER,
                performed_by VARCHAR(100),
                performed_at TIMESTAMP NOT NULL,
                archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
            
            self.db.execute_sql(sql_create_archive)
            
            # 开始事务
            sql_begin = "BEGIN;"
            self.db.execute_sql(sql_begin)
            
            # 复制旧日志到归档表
            sql_copy = f"""
            INSERT INTO backend_education_logs_archive
            (id, action, entity_type, entity_id, change_amount, previous_value, new_value, performed_by, performed_at)
            SELECT id, action, entity_type, entity_id, change_amount, previous_value, new_value, performed_by, performed_at
            FROM backend_education_logs
            WHERE performed_at < ?
            """
            
            copy_result = self.db.execute_sql(sql_copy, (cutoff_date_str,))
            
            # 删除已归档的日志
            sql_delete = """
            DELETE FROM backend_education_logs
            WHERE performed_at < ?
            """
            
            delete_result = self.db.execute_sql(sql_delete, (cutoff_date_str,))
            
            # 提交事务
            sql_commit = "COMMIT;"
            self.db.execute_sql(sql_commit)
            
            archive_count = copy_result.rowcount if hasattr(copy_result, 'rowcount') else 0
            
            self.logger.info(f"Successfully archived {archive_count} logs older than {days_old} days")
            return {
                "success": True,
                "logs_archived": archive_count,
                "cutoff_date": cutoff_date_str
            }
            
        except Exception as e:
            # 回滚事务
            sql_rollback = "ROLLBACK;"
            self.db.execute_sql(sql_rollback)
            
            self.logger.error(f"Failed to archive logs: {str(e)}")
            raise UpdateError(f"Failed to archive logs: {str(e)}")

# ========== 测试用例和示例 ==========
import asyncio
import random
from concurrent.futures import ThreadPoolExecutor
def initialize_education_database(db_path: str) -> None:
    """
    初始化教育系统数据库，创建必要的表结构。
    
    Args:
        db_path (str): SQLite数据库文件路径
    """
    import sqlite3  # 局部导入也能解决问题
    import os
    
    try:
        # 检查数据库文件是否已存在，若存在则删除
        if os.path.exists(db_path):
            os.remove(db_path)
            
        # 连接SQLite数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建教学机构表
        cursor.execute('''
        CREATE TABLE backend_institutions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            contact_person TEXT,
            contact_email TEXT,
            contact_phone TEXT,
            max_subjects INTEGER DEFAULT 5,
            current_subjects INTEGER DEFAULT 0,
            max_students INTEGER DEFAULT 100,
            current_students INTEGER DEFAULT 0,
            token_quota INTEGER DEFAULT 1000000,
            token_used INTEGER DEFAULT 0,
            question_quota INTEGER DEFAULT 10000,
            questions_asked INTEGER DEFAULT 0,
            status TEXT DEFAULT 'active',
            valid_until TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建科目表
        cursor.execute('''
        CREATE TABLE backend_subjects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            institution_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            max_students INTEGER DEFAULT 30,
            current_students INTEGER DEFAULT 0,
            token_quota INTEGER DEFAULT 100000,
            token_used INTEGER DEFAULT 0,
            question_quota INTEGER DEFAULT 1000,
            questions_asked INTEGER DEFAULT 0,
            status TEXT DEFAULT 'active',
            valid_until TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (institution_id) REFERENCES backend_institutions (id)
        )
        ''')
        
        # 创建学生表
        cursor.execute('''
        CREATE TABLE backend_students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            wechat_id TEXT,
            email TEXT,
            phone TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建学生-机构关联表
        cursor.execute('''
        CREATE TABLE backend_student_institutions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            institution_id INTEGER NOT NULL,
            status TEXT DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES backend_students (id),
            FOREIGN KEY (institution_id) REFERENCES backend_institutions (id),
            UNIQUE (student_id, institution_id)
        )
        ''')
        
        # 创建学生报名表
        cursor.execute('''
        CREATE TABLE backend_student_enrollments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            subject_id INTEGER NOT NULL,
            token_quota INTEGER DEFAULT 10000,
            token_used INTEGER DEFAULT 0,
            question_quota INTEGER DEFAULT 100,
            questions_asked INTEGER DEFAULT 0,
            status TEXT DEFAULT 'active',
            valid_until TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES backend_students (id),
            FOREIGN KEY (subject_id) REFERENCES backend_subjects (id),
            UNIQUE (student_id, subject_id)
        )
        ''')
        
        # 创建日志表
        cursor.execute('''
        CREATE TABLE backend_education_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            action TEXT NOT NULL,
            entity_type TEXT NOT NULL,
            entity_id INTEGER NOT NULL,
            change_amount INTEGER,
            previous_value INTEGER,
            new_value INTEGER,
            performed_by TEXT,
            performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建机构使用统计视图
        cursor.execute('''
        CREATE VIEW backend_institution_usage_stats AS
        SELECT 
            i.id,
            i.name,
            i.description,
            i.contact_person,
            i.contact_email,
            i.contact_phone,
            i.max_subjects,
            (SELECT COUNT(*) FROM backend_subjects WHERE institution_id = i.id AND status = 'active') AS current_subjects,
            i.max_students,
            (SELECT COUNT(DISTINCT student_id) FROM backend_student_institutions WHERE institution_id = i.id AND status = 'active') AS current_students,
            i.token_quota,
            i.token_used,
            i.question_quota,
            i.questions_asked,
            i.status,
            i.valid_until,
            i.created_at,
            i.updated_at
        FROM backend_institutions i
        ''')
        
        # 提交事务并关闭连接
        conn.commit()
        conn.close()
        
        print("Successfully created database tables using embedded SQL")
        
    except Exception as e:
        print(f"Error initializing database: {str(e)}")
        if 'conn' in locals():
            try:
                conn.close()
            except:
                pass

# 添加到文件末尾，替换原有的run_test_cases函数

def run_enhanced_test_cases(edu_db: EducationDBManager) -> bool:
    """
    运行增强版测试用例，包含多机构、共享科目和交叉报名场景。
    
    Args:
        edu_db (EducationDBManager): 教育系统数据库管理器实例
        
    Returns:
        bool: 测试是否成功
    """
    try:
        print("\n========== 开始运行增强版测试用例 ==========")
        
        # 1. 创建两个教学机构
        print("\n[测试] 创建多个教学机构...")
        institution1 = edu_db.create_institution(
            name="AI编程学院",
            contact_person="张主任",
            contact_email="<EMAIL>",
            max_subjects=5,
            max_students=100,
            token_quota=2000000,
            question_quota=20000
        )
        print(f"成功创建机构: {institution1['name']}, ID: {institution1['id']}")
        
        institution2 = edu_db.create_institution(
            name="数据科学研究所", 
            contact_person="李研究员",
            contact_email="<EMAIL>",
            max_subjects=8,
            max_students=150,
            token_quota=3000000,
            question_quota=30000
        )
        print(f"成功创建机构: {institution2['name']}, ID: {institution2['id']}")
        
        # 2. 创建科目 - 两个机构共有和私有的科目
        print("\n[测试] 创建共享和私有科目...")
        # 机构1的科目
        subject1_1 = edu_db.create_subject(
            institution_id=institution1["id"],
            name="Python基础编程",
            max_students=30,
            token_quota=200000,
            question_quota=2000
        )
        print(f"成功为机构1创建科目: {subject1_1['name']}, ID: {subject1_1['id']}")
        
        subject1_2 = edu_db.create_subject(
            institution_id=institution1["id"],
            name="人工智能导论",
            max_students=25,
            token_quota=300000,
            question_quota=3000
        )
        print(f"成功为机构1创建科目: {subject1_2['name']}, ID: {subject1_2['id']}")
        
        # 机构2的科目
        subject2_1 = edu_db.create_subject(
            institution_id=institution2["id"],
            name="Python基础编程", # 与机构1相同名称
            max_students=40,
            token_quota=250000,
            question_quota=2500
        )
        print(f"成功为机构2创建科目: {subject2_1['name']}, ID: {subject2_1['id']}")
        
        subject2_2 = edu_db.create_subject(
            institution_id=institution2["id"],
            name="数据分析与可视化",
            max_students=35,
            token_quota=280000,
            question_quota=2800
        )
        print(f"成功为机构2创建科目: {subject2_2['name']}, ID: {subject2_2['id']}")
        
        subject2_3 = edu_db.create_subject(
            institution_id=institution2["id"],
            name="机器学习基础",
            max_students=30,
            token_quota=350000,
            question_quota=3500
        )
        print(f"成功为机构2创建科目: {subject2_3['name']}, ID: {subject2_3['id']}")
        
        # 3. 创建多个学生
        print("\n[测试] 创建多个学生...")
        student1 = edu_db.create_student(name="王同学", wechat_id="wx_wang123")
        print(f"成功创建学生: {student1['name']}, ID: {student1['id']}")
        
        student2 = edu_db.create_student(name="李同学", wechat_id="wx_li456")
        print(f"成功创建学生: {student2['name']}, ID: {student2['id']}")
        
        student3 = edu_db.create_student(name="赵同学", wechat_id="wx_zhao789")
        print(f"成功创建学生: {student3['name']}, ID: {student3['id']}")
        
        # 4. 创建跨机构的复杂报名关系
        print("\n[测试] 创建跨机构的报名关系...")
        
        # 学生1同时报名两个机构的Python课程
        enrollment1_1 = edu_db.enroll_student(
            student_id=student1['id'],
            subject_id=subject1_1['id'],  # 机构1的Python课程
            token_quota=50000,
            question_quota=500
        )
        print(f"学生 {student1['name']} 成功报名机构1的 {subject1_1['name']}")
        
        enrollment1_2 = edu_db.enroll_student(
            student_id=student1['id'],
            subject_id=subject2_1['id'],  # 机构2的Python课程
            token_quota=60000,
            question_quota=600
        )
        print(f"学生 {student1['name']} 成功报名机构2的 {subject2_1['name']}")
        
        # 学生2报名机构1的所有课程
        enrollment2_1 = edu_db.enroll_student(
            student_id=student2['id'],
            subject_id=subject1_1['id'],
            token_quota=45000,
            question_quota=450
        )
        print(f"学生 {student2['name']} 成功报名机构1的 {subject1_1['name']}")
        
        enrollment2_2 = edu_db.enroll_student(
            student_id=student2['id'],
            subject_id=subject1_2['id'],
            token_quota=55000,
            question_quota=550
        )
        print(f"学生 {student2['name']} 成功报名机构1的 {subject1_2['name']}")
        
        # 学生3报名机构2的所有课程
        enrollment3_1 = edu_db.enroll_student(
            student_id=student3['id'],
            subject_id=subject2_1['id'],
            token_quota=50000,
            question_quota=500
        )
        print(f"学生 {student3['name']} 成功报名机构2的 {subject2_1['name']}")
        
        enrollment3_2 = edu_db.enroll_student(
            student_id=student3['id'],
            subject_id=subject2_2['id'],
            token_quota=55000,
            question_quota=550
        )
        print(f"学生 {student3['name']} 成功报名机构2的 {subject2_2['name']}")
        
        enrollment3_3 = edu_db.enroll_student(
            student_id=student3['id'],
            subject_id=subject2_3['id'],
            token_quota=60000,
            question_quota=600
        )
        print(f"学生 {student3['name']} 成功报名机构2的 {subject2_3['name']}")
        
        # 5. 给不同机构记录token使用量
        print("\n[测试] 给不同机构记录token使用量...")
        
        # 机构1的使用量
        edu_db.increment_token_usage(
            entity_type="enrollment",
            entity_id=enrollment1_1['id'],
            amount=5000,
            performed_by="test_system"
        )
        print(f"成功增加学生 {student1['name']} 在机构1 {subject1_1['name']} 的token使用量: 5000")
        
        edu_db.increment_token_usage(
            entity_type="enrollment",
            entity_id=enrollment2_2['id'],
            amount=8000,
            performed_by="test_system"
        )
        print(f"成功增加学生 {student2['name']} 在机构1 {subject1_2['name']} 的token使用量: 8000")
        
        # 机构2的使用量
        edu_db.increment_token_usage(
            entity_type="enrollment",
            entity_id=enrollment1_2['id'],
            amount=7000,
            performed_by="test_system"
        )
        print(f"成功增加学生 {student1['name']} 在机构2 {subject2_1['name']} 的token使用量: 7000")
        
        edu_db.increment_token_usage(
            entity_type="enrollment",
            entity_id=enrollment3_3['id'],
            amount=9000,
            performed_by="test_system"
        )
        print(f"成功增加学生 {student3['name']} 在机构2 {subject2_3['name']} 的token使用量: 9000")
        
        # 6. 给不同机构记录问题使用量
        print("\n[测试] 给不同机构记录问题使用量...")
        
        # 机构1的使用量
        edu_db.increment_question_count(
            entity_type="enrollment",
            entity_id=enrollment1_1['id'],
            amount=50,
            performed_by="test_system"
        )
        print(f"成功增加学生 {student1['name']} 在机构1 {subject1_1['name']} 的问题使用量: 50")
        
        # 机构2的使用量
        edu_db.increment_question_count(
            entity_type="enrollment",
            entity_id=enrollment3_2['id'],
            amount=80,
            performed_by="test_system"
        )
        print(f"成功增加学生 {student3['name']} 在机构2 {subject2_2['name']} 的问题使用量: 80")
        
        # 7. 获取和比较两个机构的使用统计
        print("\n[测试] 比较多机构使用统计...")
        stats1 = edu_db.get_institution_usage_stats(institution1['id'])
        stats2 = edu_db.get_institution_usage_stats(institution2['id'])
        
        print(f"\n机构1 '{institution1['name']}' 使用统计:")
        print(f"- 科目数: {stats1['current_subjects']}/{stats1['max_subjects']}")
        print(f"- 学生数: {stats1['current_students']}/{stats1['max_students']}")
        print(f"- Token使用: {stats1['token_used']}/{stats1['token_quota']}")
        print(f"- 问题使用: {stats1['questions_asked']}/{stats1['question_quota']}")
        
        print(f"\n机构2 '{institution2['name']}' 使用统计:")
        print(f"- 科目数: {stats2['current_subjects']}/{stats2['max_subjects']}")
        print(f"- 学生数: {stats2['current_students']}/{stats2['max_students']}")
        print(f"- Token使用: {stats2['token_used']}/{stats2['token_quota']}")
        print(f"- 问题使用: {stats2['questions_asked']}/{stats2['question_quota']}")
        
        # 8. 测试查询同名科目的学生数
        print("\n[测试] 查询不同机构同名科目的学生数...")
        python_subject1 = edu_db.get_subject(subject1_1['id'])
        python_subject2 = edu_db.get_subject(subject2_1['id'])
        
        print(f"机构1 '{institution1['name']}' 的Python课程学生数: {python_subject1['current_students']}")
        print(f"机构2 '{institution2['name']}' 的Python课程学生数: {python_subject2['current_students']}")
        
        # 9. 生成每个机构的报告
        print("\n[测试] 生成多机构报告...")
        report1 = edu_db.generate_institution_report(institution1['id'], as_markdown=True)
        report2 = edu_db.generate_institution_report(institution2['id'], as_markdown=True)
        
        print(f"成功生成机构1报告，前300字符:")
        print(report1[:300] + "...\n")
        
        print(f"成功生成机构2报告，前300字符:")
        print(report2[:300] + "...\n")
        
        # 10. 配额管理 - 调整两个机构的配额
        print("\n[测试] 跨机构调整配额...")
        result1 = edu_db.adjust_quota(
            entity_type="subject",
            entity_id=subject1_1['id'],
            quota_type="token",
            new_quota=250000
        )
        print(f"成功调整机构1 {subject1_1['name']} 的token配额: {result1['previous_quota']} -> {result1['new_quota']}")
        
        result2 = edu_db.adjust_quota(
            entity_type="subject",
            entity_id=subject2_3['id'],
            quota_type="question",
            new_quota=4000
        )
        print(f"成功调整机构2 {subject2_3['name']} 的问题配额: {result2['previous_quota']} -> {result2['new_quota']}")
        
        # 11. 测试跨机构学生搜索
        print("\n[测试] 跨机构搜索学生...")
        search_results = edu_db.search_students("同学")
        print(f"在所有机构中搜索到 {len(search_results)} 个学生")
        
        # 如果一个学生在多个机构，验证是否正确返回所有机构
        for i, student in enumerate(search_results):
            if 'institutions' in student:
                print(f"学生 {student['name']} 报名的机构数: {len(student['institutions'])}")
                for inst in student['institutions']:
                    print(f"  - 机构: {inst['name']} (ID: {inst['id']})")
        
        print("\n========== 增强版测试用例运行完成 ==========")
        return True
    
    except Exception as e:
        print(f"\n[错误] 增强版测试用例执行失败: {str(e)}")
        return False

def run_test_cases(edu_db: EducationDBManager) -> bool:
    """
    运行基本测试用例。
    
    Args:
        edu_db (EducationDBManager): 教育系统数据库管理器实例
        
    Returns:
        bool: 测试是否成功
    """
    try:
        print("\n========== 开始运行基本测试用例 ==========")
        
        # 1. 创建一个教学机构
        print("\n[测试] 创建教学机构...")
        institution = edu_db.create_institution(
            name="测试教育机构",
            contact_person="测试管理员",
            contact_email="<EMAIL>",
            max_subjects=5,
            max_students=100,
            token_quota=500000,
            question_quota=5000
        )
        print(f"成功创建机构: {institution['name']}, ID: {institution['id']}")
        
        # 2. 创建科目
        print("\n[测试] 创建科目...")
        subject = edu_db.create_subject(
            institution_id=institution["id"],
            name="测试科目",
            max_students=30,
            token_quota=100000,
            question_quota=1000
        )
        print(f"成功创建科目: {subject['name']}, ID: {subject['id']}")
        
        # 3. 创建学生
        print("\n[测试] 创建学生...")
        student = edu_db.create_student(name="测试学生", wechat_id="wx_test123")
        print(f"成功创建学生: {student['name']}, ID: {student['id']}")
        
        # 4. 报名
        print("\n[测试] 学生报名...")
        enrollment = edu_db.enroll_student(
            student_id=student['id'],
            subject_id=subject['id'],
            token_quota=10000,
            question_quota=100
        )
        print(f"学生 {student['name']} 成功报名 {subject['name']}")
        
        # 5. 使用统计
        print("\n[测试] 查看使用统计...")
        stats = edu_db.get_institution_usage_stats(institution['id'])
        print(f"机构统计: 科目数: {stats['current_subjects']}/{stats['max_subjects']}")
        print(f"机构统计: 学生数: {stats['current_students']}/{stats['max_students']}")
        
        # 6. 生成报告
        print("\n[测试] 生成报告...")
        report = edu_db.generate_institution_report(institution['id'], as_markdown=True)
        print(f"成功生成报告，前100字符:\n{report[:100]}...")
        
        print("\n========== 基本测试用例运行完成 ==========")
        return True
        
    except Exception as e:
        print(f"\n[错误] 测试用例执行失败: {str(e)}")
        return False
    
async def run_concurrent_test_cases(edu_db: EducationDBManager) -> bool:
    """
    运行真正的并发测试用例，使用多进程模拟竞争条件。
    """
    try:
        print("\n========== 开始运行真正的并发测试用例 ==========")
        
        # 创建测试数据
        institution = edu_db.create_institution(
            name="并发测试学院",
            max_subjects=10,
            max_students=200,
            token_quota=1000000,
            question_quota=10000
        )
        print(f"创建机构: {institution['name']}")
        
        # 创建科目
        subject = edu_db.create_subject(
            institution_id=institution["id"],
            name="并发科目",
            max_students=50,
            token_quota=100000,
            question_quota=1000
        )
        print(f"创建科目: {subject['name']}")
        
        # 创建学生
        students = []
        for i in range(5):
            student = edu_db.create_student(name=f"并发学生{i+1}")
            students.append(student)
            print(f"创建学生: {student['name']}")
        
        # 先让所有学生报名
        enrollments = []
        for student in students:
            enrollment = edu_db.enroll_student(
                student_id=student['id'],
                subject_id=subject['id'],
                token_quota=10000,
                question_quota=100
            )
            enrollments.append(enrollment)
            print(f"学生 {student['name']} 报名成功")
        
        # 定义进程要执行的函数
        def increment_token_process(db_path, enrollment_id, amount):
            # 每个进程创建自己的数据库连接
            from utils.db.db_connector import DBConnector
            from utils.db.education_db_manager import EducationDBManager
            
            db = DBConnector(sqlite_path=db_path)
            edu_manager = EducationDBManager(db)
            
            try:
                result = edu_manager.increment_token_usage(
                    "enrollment", 
                    enrollment_id, 
                    amount, 
                    f"process_{multiprocessing.current_process().name}"
                )
                return True, f"进程 {multiprocessing.current_process().name} 成功增加 {amount} tokens"
            except Exception as e:
                return False, f"进程 {multiprocessing.current_process().name} 失败: {str(e)}"
        
        # 获取数据库路径
        db_path = edu_db.db.sqlite_path
        
        # 创建进程池
        print("\n[测试] 启动真正的并发测试...")
        with multiprocessing.Pool(processes=5) as pool:
            # 准备任务 - 每个报名记录有多个进程同时增加token
            tasks = []
            for enrollment in enrollments:
                # 每个报名记录有3个进程同时操作
                for _ in range(3):
                    amount = random.randint(100, 500)
                    tasks.append((db_path, enrollment['id'], amount))
            
            # 并发执行任务
            results = pool.starmap(increment_token_process, tasks)
            
            # 处理结果
            success_count = sum(1 for success, _ in results if success)
            print(f"并发操作结果: {success_count}/{len(tasks)} 成功")
            
            # 打印详细结果
            for success, message in results:
                print(message)
        
        # 验证最终状态
        print("\n[测试] 验证数据一致性...")
        final_stats = edu_db.get_institution_usage_stats(institution['id'])
        print(f"机构总token使用: {final_stats['token_used']}")
        
        # 检查每个报名记录的使用量
        total_enrollment_used = 0
        for enrollment in enrollments:
            details = edu_db.get_enrollment(enrollment['id'])
            total_enrollment_used += details['token_used']
            print(f"报名ID {enrollment['id']} token使用: {details['token_used']}")
        
        print(f"所有报名记录token使用总和: {total_enrollment_used}")
        
        # 检查数据一致性
        if final_stats['token_used'] == total_enrollment_used:
            print("数据一致性验证通过: 机构使用量等于所有报名记录使用量之和")
        else:
            print(f"数据一致性验证失败: 机构使用量({final_stats['token_used']})与报名记录总和({total_enrollment_used})不匹配")
        
        print("\n========== 并发测试用例运行完成 ==========")
        return True
        
    except Exception as e:
        print(f"\n[错误] 并发测试用例执行失败: {str(e)}")
        return False
    
def main():
    """
    主函数: 创建数据库连接, 初始化数据库, 并运行测试用例。
    """
    import os
    import logging
    import asyncio
    from utils.db.db_connector import DBConnector
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建临时数据库文件
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'test_education.db')
    print(f"使用数据库路径: {db_path}")
    
    try:
        # 初始化数据库
        initialize_education_database(db_path)
        
        # 创建数据库连接器
        db_connector = DBConnector(sqlite_path=db_path)
        
        # 创建教育系统数据库管理器
        edu_db = EducationDBManager(db_connector)
        
        # 选择测试类型
        test_type = input("\n选择测试类型 (1=标准测试, 2=增强版多机构测试, 3=并发测试) [默认=2]: ").strip() or "2"
        
        # 运行测试用例
        if test_type == "1":
            success = run_test_cases(edu_db)
        elif test_type == "3":
            # 运行异步并发测试
            success = asyncio.run(run_concurrent_test_cases(edu_db))
        else:
            success = run_enhanced_test_cases(edu_db)
        
        print(f"\n测试结果: {'成功' if success else '失败'}")
        
        # 提示是否保留测试数据库
        keep_db = input("\n是否保留测试数据库? (y/n): ").lower() == 'y'
        if not keep_db and os.path.exists(db_path):
            os.remove(db_path)
            print(f"已删除测试数据库: {db_path}")
        else:
            print(f"测试数据库已保留: {db_path}")
    
    except Exception as e:
        print(f"执行过程中出现错误: {str(e)}")
        # 确保即使出错也清理测试数据库
        if os.path.exists(db_path) and input("\n是否保留测试数据库? (y/n): ").lower() != 'y':
            os.remove(db_path)
            print(f"已删除测试数据库: {db_path}")

if __name__ == "__main__":
    main() 