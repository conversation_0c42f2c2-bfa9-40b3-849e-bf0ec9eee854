# ai-backend-system/utils/db/db_connector.py
"""
定义 DBConnector 类。

该 Util 负责连接和操作数据库系统 (包括 Supabase/PostgreSQL、SQLite 和 pgvector 扩展)，提供统一的接口与数据库进行交互。
作为 Human-agents block 与数据库 AI运营相关表 交互的桥梁。

核心功能包括：
- 连接 Supabase PostgreSQL 数据库
- 连接 SQLite 数据库
- 连接 Supabase Vector 数据库 (pgvector 扩展)
- 向指定表插入数据 (INSERT)
- 从指定表获取数据 (SELECT)
- 查询向量数据库，进行向量相似度检索
- 更新表数据 (UPDATE)
- 删除表数据 (DELETE)
- 执行更复杂的 SQL 查询
"""
import os
import json
import logging
import sqlite3
from typing import List, Dict, Any, Optional, Union, Tuple
import numpy as np
import asyncio
import aiosqlite
import uuid
import random

# Conditionally import Supabase
try:
    from supabase.client import Client, create_client
    from supabase.lib.client_options import ClientOptions
    from supabase import PostgrestFilterBuilder
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    # Create dummy classes to avoid errors
    class PostgrestFilterBuilder:
        pass

# Langchain 集成
from langchain_community.vectorstores.supabase import SupabaseVectorStore
from langchain_core.embeddings import Embeddings
from langchain_core.documents import Document

# 自定义异常
class DBConnectorError(Exception):
    """DBConnector 相关异常的基类"""
    pass

class ConnectionError(DBConnectorError):
    """数据库连接错误"""
    pass

class QueryError(DBConnectorError):
    """查询执行错误"""
    pass

class InsertError(DBConnectorError):
    """数据插入错误"""
    pass

class UpdateError(DBConnectorError):
    """数据更新错误"""
    pass

class DeleteError(DBConnectorError):
    """数据删除错误"""
    pass

class VectorOperationError(DBConnectorError):
    """向量操作错误"""
    pass

class DBConnector:
    """
    DBConnector, 负责连接和操作数据库系统 (Supabase/PostgreSQL/SQLite - AI运营相关表).
    作为 Human-agents block 与数据库 AI运营相关表 交互的桥梁.
    """
    def __init__(self, 
                 supabase_url: Optional[str] = None, 
                 supabase_key: Optional[str] = None, 
                 sqlite_path: Optional[str] = None,
                 embedding_model: Optional[Embeddings] = None):
        """
        初始化 DBConnector 实例。

        Args:
            supabase_url (Optional[str]): Supabase 项目 URL，例如 "https://your-project-id.supabase.co"。
            supabase_key (Optional[str]): Supabase API 密钥 (Service Role 密钥，用于后端服务访问数据库)。
            sqlite_path (Optional[str]): SQLite 数据库文件路径，例如 "database.db"。
            embedding_model (Optional[Embeddings]): 用于生成向量嵌入的模型。如果提供，将用于向量数据库操作。
        
        Raises:
            ConnectionError: 连接数据库失败时抛出。
        """
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key
        self.sqlite_path = sqlite_path
        self.embedding_model = embedding_model
        self.supabase_client = None
        self.sqlite_conn = None
        
        # 初始化数据库连接
        if supabase_url and supabase_key:
            self._init_supabase()
        elif sqlite_path:
            self._init_sqlite()
        else:
            raise ConnectionError("Either Supabase credentials or SQLite path must be provided")
    
    def _init_supabase(self):
        """初始化 Supabase 连接"""
        try:
            # 初始化 Supabase 客户端
            options = ClientOptions(schema="public")
            self.supabase_client: Client = create_client(self.supabase_url, self.supabase_key, options=options)
            
            # 测试连接
            self.supabase_client.table("_dummy").select("*").limit(1).execute()
            logging.info(f"Successfully connected to Supabase at {self.supabase_url}")
        except Exception as e:
            logging.error(f"Failed to connect to Supabase: {str(e)}")
            raise ConnectionError(f"Failed to connect to Supabase: {str(e)}")
    
    def _init_sqlite(self):
        """初始化 SQLite 连接"""
        try:
            self.sqlite_conn = sqlite3.connect(self.sqlite_path)
            # 启用外键约束
            self.sqlite_conn.execute("PRAGMA foreign_keys = ON")
            # 启用递归触发器
            self.sqlite_conn.execute("PRAGMA recursive_triggers = ON")
            # 配置 SQLite 返回行为字典形式
            self.sqlite_conn.row_factory = sqlite3.Row
            logging.info(f"Successfully connected to SQLite at {self.sqlite_path}")
        except Exception as e:
            logging.error(f"Failed to connect to SQLite: {str(e)}")
            raise ConnectionError(f"Failed to connect to SQLite: {str(e)}")
    
    def _is_using_supabase(self) -> bool:
        """检查是否使用 Supabase"""
        return self.supabase_client is not None
    
    # ==================== 结构化数据写入接口 ====================
    
    def insert_data(self, table_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        向指定的表插入单条数据。

        Args:
            table_name (str): 要插入数据的表名。
            data (Dict[str, Any]): 要插入的数据，以字典形式表示。

        Returns:
            Dict[str, Any]: 操作结果，包含插入的数据。

        Raises:
            InsertError: 插入数据失败时抛出。
        """
        try:
            if self._is_using_supabase():
                # Supabase 插入
                response = self.supabase_client.table(table_name).insert(data).execute()
                
                # 检查响应
                if hasattr(response, 'error') and response.error is not None:
                    raise InsertError(f"Failed to insert data into {table_name}: {response.error}")
                
                logging.info(f"Successfully inserted data into {table_name}")
                return response.data[0] if response.data else {}
            else:
                # SQLite 插入
                cursor = self.sqlite_conn.cursor()
                
                # 构建 SQL 语句
                columns = ', '.join(data.keys())
                placeholders = ', '.join(['?' for _ in data])
                sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
                
                # 执行插入
                cursor.execute(sql, list(data.values()))
                self.sqlite_conn.commit()
                
                # 获取插入的 ID
                last_id = cursor.lastrowid
                
                # 获取插入的数据
                cursor.execute(f"SELECT * FROM {table_name} WHERE rowid = ?", (last_id,))
                result = dict(cursor.fetchone())
                
                logging.info(f"Successfully inserted data into {table_name}")
                return result
        
        except Exception as e:
            if isinstance(e, InsertError):
                raise
            logging.error(f"Error inserting data into {table_name}: {str(e)}")
            raise InsertError(f"Failed to insert data into {table_name}: {str(e)}")
    
    def batch_insert_data(self, table_name: str, data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        向指定的表批量插入多条数据。

        Args:
            table_name (str): 要插入数据的表名。
            data_list (List[Dict[str, Any]]): 要插入的数据列表。

        Returns:
            List[Dict[str, Any]]: 操作结果，包含所有插入的数据。

        Raises:
            InsertError: 批量插入数据失败时抛出。
        """
        if not data_list:
            logging.warning(f"Empty data list provided for batch insert into {table_name}")
            return []
        
        try:
            if self._is_using_supabase():
                # Supabase 批量插入
                response = self.supabase_client.table(table_name).insert(data_list).execute()
                
                # 检查响应
                if hasattr(response, 'error') and response.error is not None:
                    raise InsertError(f"Failed to batch insert data into {table_name}: {response.error}")
                
                logging.info(f"Successfully batch inserted {len(data_list)} records into {table_name}")
                return response.data
            else:
                # SQLite 批量插入
                cursor = self.sqlite_conn.cursor()
                results = []
                
                # 开始事务
                self.sqlite_conn.execute("BEGIN TRANSACTION")
                
                try:
                    for data in data_list:
                        # 构建 SQL 语句
                        columns = ', '.join(data.keys())
                        placeholders = ', '.join(['?' for _ in data])
                        sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
                        
                        # 执行插入
                        cursor.execute(sql, list(data.values()))
                        last_id = cursor.lastrowid
                        
                        # 获取插入的数据
                        cursor.execute(f"SELECT * FROM {table_name} WHERE rowid = ?", (last_id,))
                        result = dict(cursor.fetchone())
                        results.append(result)
                    
                    # 提交事务
                    self.sqlite_conn.commit()
                    
                    logging.info(f"Successfully batch inserted {len(data_list)} records into {table_name}")
                    return results
                except Exception as e:
                    # 回滚事务
                    self.sqlite_conn.rollback()
                    raise e
        
        except Exception as e:
            if isinstance(e, InsertError):
                raise
            logging.error(f"Error batch inserting data into {table_name}: {str(e)}")
            raise InsertError(f"Failed to batch insert data into {table_name}: {str(e)}")
    
    # ==================== 结构化数据查询接口 ====================
    
    def fetch_data(self, table_name: str, query_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        从指定的表中获取数据。

        Args:
            table_name (str): 表名
            query_params (Dict[str, Any], optional): 查询参数，支持等于、大于、小于等条件

        Returns:
            List[Dict[str, Any]]: 查询结果列表

        Raises:
            QueryError: 查询执行失败时抛出
        """
        try:
            if self._is_using_supabase():
                # 开始构建查询
                query = self.supabase_client.table(table_name)
                
                # 如果没有查询参数，直接返回所有记录
                if not query_params:
                    response = query.select('*').execute()
                    return response.data
                
                # 使用辅助方法处理查询条件
                query = self._format_query_params(query_params, query)
                
                # 执行查询
                response = query.execute()
                
                return response.data
            else:
                # SQLite 查询实现
                cursor = self.sqlite_conn.cursor()
                
                # 构建SQL查询
                select_fields = query_params.get('select', '*') if query_params else '*'
                sql = f"SELECT {select_fields} FROM {table_name}"
                
                # 处理查询条件
                where_clauses = []
                where_values = []
                
                if query_params:
                    # 等于条件
                    if 'eq' in query_params:
                        for column, value in query_params['eq'].items():
                            where_clauses.append(f"{column} = ?")
                            where_values.append(value)
                    
                    # 大于条件
                    if 'gt' in query_params:
                        for column, value in query_params['gt'].items():
                            where_clauses.append(f"{column} > ?")
                            where_values.append(value)
                    
                    # 小于条件
                    if 'lt' in query_params:
                        for column, value in query_params['lt'].items():
                            where_clauses.append(f"{column} < ?")
                            where_values.append(value)
                    
                    # 大于等于条件
                    if 'gte' in query_params:
                        for column, value in query_params['gte'].items():
                            where_clauses.append(f"{column} >= ?")
                            where_values.append(value)
                    
                    # 小于等于条件
                    if 'lte' in query_params:
                        for column, value in query_params['lte'].items():
                            where_clauses.append(f"{column} <= ?")
                            where_values.append(value)
                    
                    # 模糊匹配
                    if 'like' in query_params:
                        for column, pattern in query_params['like'].items():
                            where_clauses.append(f"{column} LIKE ?")
                            where_values.append(pattern)
                    
                    # 添加WHERE子句
                    if where_clauses:
                        sql += " WHERE " + " AND ".join(where_clauses)
                
                # 处理排序
                if 'order' in query_params:
                    order_info = query_params['order']
                    column = order_info.get('column')
                    ascending = order_info.get('ascending', True)
                    if column:
                        sql += f" ORDER BY {column} {'ASC' if ascending else 'DESC'}"
                
                # 处理分页
                if 'limit' in query_params:
                    sql += f" LIMIT {query_params['limit']}"
                    
                    if 'offset' in query_params:
                        sql += f" OFFSET {query_params['offset']}"
                
                # 执行查询
                cursor.execute(sql, where_values)
                rows = cursor.fetchall()
                
                # 将结果转换为字典列表
                result = []
                for row in rows:
                    result.append(dict(row))
                
                return result
        except Exception as e:
            logging.error(f"Error fetching data from {table_name}: {str(e)}")
            raise QueryError(f"Failed to fetch data from {table_name}: {str(e)}")
    
    def update_data(self, table_name: str, update_data: Dict[str, Any], filter_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新表中符合条件的数据。
        
        Args:
            table_name (str): 要更新的表名。
            update_data (Dict[str, Any]): 要更新的数据，例如 {'name': 'new_name', 'status': 'active'}。
            filter_params (Dict[str, Any]): 过滤条件，可以是直接条件如 {'id': 123} 或嵌套条件如 {'eq': {'id': 123}}。
            
        Returns:
            Dict[str, Any]: 更新后的数据。

        Raises:
            UpdateError: 更新数据失败时抛出。
        """
        if not update_data:
            return {}
        
        try:
            # 处理嵌套过滤条件 {'eq': {'id': 123}}
            actual_filter = {}
            if 'eq' in filter_params and isinstance(filter_params['eq'], dict):
                actual_filter = filter_params['eq']
            else:
                actual_filter = filter_params
            
            if self._is_using_supabase():
                # Supabase 更新
                query = self.supabase_client.table(table_name)
                
                # 添加过滤条件
                for column, value in actual_filter.items():
                    query = query.eq(column, value)
                
                # 执行更新
                response = query.update(update_data).execute()
            
                # 检查响应
                if hasattr(response, 'error') and response.error is not None:
                    raise UpdateError(f"Failed to update data in {table_name}: {response.error}")
                
                logging.info(f"Successfully updated data in {table_name}")
                return response.data[0] if response.data else {}
            else:
                # SQLite 更新
                cursor = self.sqlite_conn.cursor()
                
                # 构建SET子句
                set_clauses = []
                set_values = []
                for key, value in update_data.items():
                    set_clauses.append(f"{key} = ?")
                    set_values.append(value)
                
                # 构建WHERE子句
                where_clauses = []
                where_values = []
                for key, value in actual_filter.items():
                    where_clauses.append(f"{key} = ?")
                    where_values.append(value)
                
                # 构建完整SQL
                sql = f"UPDATE {table_name} SET {', '.join(set_clauses)} WHERE {' AND '.join(where_clauses)}"
                params = set_values + where_values
                
                # 执行更新
                cursor.execute(sql, params)
                self.sqlite_conn.commit()
                
                # 获取更新后的数据
                sql_select = f"SELECT * FROM {table_name} WHERE {' AND '.join(where_clauses)}"
                cursor.execute(sql_select, where_values)
                row = cursor.fetchone()
                
                if row:
                    return dict(row)
                return {}
        
        except Exception as e:
            if isinstance(e, UpdateError):
                raise
            logging.error(f"Error updating data in {table_name}: {str(e)}")
            raise UpdateError(f"Failed to update data in {table_name}: {str(e)}")
    
    def delete_data(self, table_name: str, filter_params: Dict[str, Any]) -> int:
        """
        从指定的表中删除符合条件的数据。
        
        Args:
            table_name (str): 要删除数据的表名。
            filter_params (Dict[str, Any]): 过滤条件，可以是直接条件如 {'id': 123} 或嵌套条件如 {'eq': {'id': 123}}。
            
        Returns:
            int: 删除的记录数量。

        Raises:
            DeleteError: 删除数据失败时抛出。
        """
        try:
            # 处理嵌套过滤条件 {'eq': {'id': 123}}
            actual_filter = {}
            if 'eq' in filter_params and isinstance(filter_params['eq'], dict):
                actual_filter = filter_params['eq']
            else:
                actual_filter = filter_params
            
            if self._is_using_supabase():
                # Supabase 删除
                query = self.supabase_client.table(table_name)
                
                # 添加过滤条件
                for column, value in actual_filter.items():
                    query = query.eq(column, value)
                
                # 执行删除
                response = query.delete().execute()
                
                # 检查响应
                if hasattr(response, 'error') and response.error is not None:
                    raise DeleteError(f"Failed to delete data from {table_name}: {response.error}")
                
                logging.info(f"Successfully deleted data from {table_name}")
                return len(response.data) if response.data else 0
            else:
                # SQLite 删除
                cursor = self.sqlite_conn.cursor()
                
                # 构建WHERE子句
                where_clauses = []
                where_values = []
                for key, value in actual_filter.items():
                    where_clauses.append(f"{key} = ?")
                    where_values.append(value)
                
                # 构建完整SQL
                sql = f"DELETE FROM {table_name} WHERE {' AND '.join(where_clauses)}"
                
                # 执行删除
                cursor.execute(sql, where_values)
                self.sqlite_conn.commit()
                
                # 获取删除的记录数量
                deleted_count = cursor.rowcount
                
                logging.info(f"Successfully deleted {deleted_count} records from {table_name}")
                return deleted_count
                
        except Exception as e:
            if isinstance(e, DeleteError):
                raise
            logging.error(f"Error deleting data from {table_name}: {str(e)}")
            raise DeleteError(f"Failed to delete data from {table_name}: {str(e)}")
    
    def execute_sql(self, sql: str, params: Any = None, as_markdown: bool = False, markdown_title: str = None) -> Union[List[Dict[str, Any]], str]:
        """
        执行自定义SQL查询。

        Args:
            sql (str): SQL查询语句
            params (Any, optional): 查询参数，可以是元组、字典等
            as_markdown (bool, optional): 是否以Markdown表格格式返回结果

        Returns:
            Union[List[Dict[str, Any]], str]: 查询结果列表或Markdown表格

        Raises:
            QueryError: 查询执行失败时抛出
        """
        try:
            if self._is_using_supabase():
                # Supabase SQL查询
                response = self.supabase_client.rpc('execute_sql', {'query': sql, 'params': params}).execute()
                
                if hasattr(response, 'error') and response.error is not None:
                    raise QueryError(f"SQL query failed: {response.error}")
                
                # 转换为Markdown表格 (如果需要)
                if as_markdown and hasattr(response, 'data') and response.data:
                    return self._convert_to_markdown_table(response.data)
                
                return response.data if hasattr(response, 'data') else []
            else:
                # SQLite查询
                cursor = self.sqlite_conn.cursor()
                cursor.row_factory = sqlite3.Row
                
                # 执行查询
                cursor.execute(sql, params or ())
                
                # 获取结果
                if cursor.description:  # 有返回结果
                    if as_markdown:
                        # 获取列名
                        columns = [description[0] for description in cursor.description]
                        
                        # 获取所有行
                        rows = cursor.fetchall()
                        
                        # 转换为Markdown
                        return self._convert_to_markdown_table([dict(row) for row in rows])
                    else:
                        # 返回字典列表
                        rows = cursor.fetchall()
                        return [dict(row) for row in rows]
                else:
                    # 非 SELECT 语句，如 INSERT, UPDATE, DELETE
                    self.sqlite_conn.commit()
                    # 对于非查询操作，返回一个包含受影响行数的字典
                    return [{"affected_rows": cursor.rowcount}]
        except Exception as e:
            logging.error(f"Error executing SQL: {str(e)}")
            # 回滚任何未完成的事务
            if hasattr(self, 'sqlite_conn') and self.sqlite_conn is not None:
                self.sqlite_conn.rollback()
            raise QueryError(f"Failed to execute SQL: {str(e)}")
    
    def execute_transaction(self, sql_statements: List[Tuple[str, Any]]) -> Any:
        """
        在一个事务中执行多个SQL语句。

        Args:
            sql_statements (List[Tuple[str, Any]]): SQL语句和参数的列表，每个元素是(sql, params)对

        Returns:
            Any: 最后一个查询的结果

        Raises:
            QueryError: 查询执行失败时抛出
        """
        try:
            if not self._is_using_supabase():
                # SQLite 事务
                cursor = self.sqlite_conn.cursor()
                cursor.row_factory = sqlite3.Row
                
                # 开始事务
                cursor.execute("BEGIN TRANSACTION")
                
                try:
                    # 执行所有语句
                    for sql, params in sql_statements:
                        cursor.execute(sql, params or ())
                    
                    # 提交事务
                    self.sqlite_conn.commit()
                    
                    # 返回最后一个查询的结果
                    if cursor.description:
                        # 如果最后一个操作是查询，返回结果
                        return [dict(row) for row in cursor.fetchall()]
                    else:
                        # 否则返回影响的行数
                        return [{"affected_rows": cursor.rowcount}]
                except Exception as e:
                    # 回滚事务
                    self.sqlite_conn.rollback()
                    raise e
            else:
                # Supabase 事务
                # 构建事务SQL (使用PostgreSQL事务语法)
                transaction_sql = "BEGIN;\n"
                
                # 参数列表
                params = []
                param_index = 1
                
                # 添加所有SQL语句
                for sql, stmt_params in sql_statements:
                    if stmt_params:
                        # 替换参数占位符 (? -> $1, $2, ...)
                        for param in stmt_params:
                            sql = sql.replace('?', f'${param_index}', 1)
                            params.append(param)
                            param_index += 1
                    
                    transaction_sql += sql + ";\n"
                
                # 添加提交语句
                transaction_sql += "COMMIT;"
                
                # 执行事务
                response = self.supabase_client.rpc('execute_sql', {'query': transaction_sql, 'params': params}).execute()
                
            if hasattr(response, 'error') and response.error is not None:
                    raise QueryError(f"Transaction failed: {response.error}")
            
            return True
        
        except Exception as e:
            if isinstance(e, QueryError):
                raise
            logging.error(f"Error executing transaction: {str(e)}")
            raise QueryError(f"Failed to execute transaction: {str(e)}")
    
    # ==================== 向量数据库接口 ====================
    
    def insert_vector_data(self, table_name: str, texts: List[str], metadata: List[Dict[str, Any]], 
                          embedding_column: str = "embedding") -> List[Dict[str, Any]]:
        """
        向向量数据库插入文本数据，自动生成向量嵌入。

        该方法接收表名 (table_name)、文本列表 (texts) 和元数据列表 (metadata) 作为输入，
        使用嵌入模型为每个文本生成向量嵌入，然后将文本、元数据和向量嵌入一起插入到指定的向量表中。

        Args:
            table_name (str): 要插入数据的向量表名。
            texts (List[str]): 要插入的文本列表。
            metadata (List[Dict[str, Any]]): 与文本对应的元数据列表，每个元素是一个字典。
            embedding_column (str, optional): 存储向量嵌入的列名。默认为 "embedding"。

        Returns:
            List[Dict[str, Any]]: 插入的记录列表。

        Raises:
            VectorOperationError: 向量操作失败时抛出，例如嵌入模型未提供、生成嵌入失败等。
        """
        if not self.embedding_model:
            raise VectorOperationError("Embedding model is required for vector operations")
        
        if len(texts) != len(metadata):
            raise VectorOperationError("The length of texts and metadata must be the same")
        
        try:
            # 生成向量嵌入
            embeddings = self.embedding_model.embed_documents(texts)
            
            # 准备插入数据
            records = []
            for i, (text, meta, embedding) in enumerate(zip(texts, metadata, embeddings)):
                record = {
                    "content": text,
                    embedding_column: embedding,
                    **meta
                }
                records.append(record)
            
            # 批量插入数据
            return self.batch_insert_data(table_name, records)
        
        except Exception as e:
            logging.error(f"Error inserting vector data into {table_name}: {str(e)}")
            raise VectorOperationError(f"Failed to insert vector data: {str(e)}")
    
    def query_vector_db(self, query_embedding: List[float], table_name: str, embedding_column: str = "embedding", 
                       match_threshold: float = 0.8, match_count: int = 10) -> List[Dict[str, Any]]:
        """
        查询向量数据库 (Supabase pgvector)，进行向量相似度检索。

        该方法接收查询向量 (query_embedding)、表名 (table_name)、向量列名 (embedding_column)、相似度匹配阈值 (match_threshold) 和返回结果数量 (match_count) 作为输入，使用 Supabase 客户端和 pgvector 扩展执行向量相似度检索。
        使用 cosine similarity 作为相似度度量方法。

        Args:
            query_embedding (List[float]): 查询向量，通常是一个浮点数列表，由 Embedding 模型生成。
            table_name (str): 向量数据所在的表名，例如 "knowledge_documents"。
            embedding_column (str, optional): 向量数据在表中的列名，例如 "embedding"。默认为 "embedding"。
            match_threshold (float, optional): 相似度匹配阈值，只有相似度评分高于该阈值的结果才会被返回。 范围通常在 0 到 1 之间，值越大表示相似度要求越高。 默认为 0.8。
            match_count (int, optional): 返回最相似的结果数量，限制返回结果列表的长度。 默认为 10。

        Returns:
            List[Dict[str, Any]]: 向量数据库匹配结果列表，每个元素是一个字典，代表一条匹配的记录，包含记录的原始数据以及相似度评分 (_similarity 字段)。
                                  结果按照相似度评分降序排列。

        Raises:
            VectorOperationError: 向量查询操作失败时抛出。
        """
        try:
            # 构建 RPC 调用参数
            rpc_params = {
                'query_embedding': query_embedding,
                'table_name': table_name,
                'embedding_column': embedding_column,
                'match_threshold': match_threshold,
                'match_count': match_count
            }
            
            # 调用 Supabase 存储过程进行向量相似度搜索
            response = self.supabase_client.rpc('match_documents', rpc_params).execute()
            
            # 检查响应
            if hasattr(response, 'error') and response.error is not None:
                raise VectorOperationError(f"Failed to query vector database: {response.error}")
            
            logging.info(f"Successfully queried vector database, found {len(response.data)} matches")
            return response.data
        
        except Exception as e:
            if isinstance(e, VectorOperationError):
                raise
            logging.error(f"Error querying vector database: {str(e)}")
            raise VectorOperationError(f"Failed to query vector database: {str(e)}")
    
    def query_vector_by_text(self, query_text: str, table_name: str, embedding_column: str = "embedding",
                           match_threshold: float = 0.8, match_count: int = 10) -> List[Dict[str, Any]]:
        """
        通过文本查询向量数据库，自动生成查询文本的向量嵌入。

        该方法接收查询文本 (query_text) 和其他参数作为输入，使用嵌入模型为查询文本生成向量嵌入，
        然后调用 query_vector_db 方法进行向量相似度检索。

        Args:
            query_text (str): 查询文本。
            table_name (str): 向量数据所在的表名。
            embedding_column (str, optional): 向量数据在表中的列名。默认为 "embedding"。
            match_threshold (float, optional): 相似度匹配阈值。默认为 0.8。
            match_count (int, optional): 返回最相似的结果数量。默认为 10。

        Returns:
            List[Dict[str, Any]]: 向量数据库匹配结果列表。

        Raises:
            VectorOperationError: 向量查询操作失败时抛出。
        """
        if not self.embedding_model:
            raise VectorOperationError("Embedding model is required for vector operations")
        
        try:
            # 生成查询文本的向量嵌入
            query_embedding = self.embedding_model.embed_query(query_text)
            
            # 调用向量查询方法
            return self.query_vector_db(
                query_embedding=query_embedding,
                table_name=table_name,
                embedding_column=embedding_column,
                match_threshold=match_threshold,
                match_count=match_count
            )
        
        except Exception as e:
            logging.error(f"Error querying vector database by text: {str(e)}")
            raise VectorOperationError(f"Failed to query vector database by text: {str(e)}")
    
    # ==================== Langchain 集成接口 ====================
    
    def get_vector_store(self, table_name: str, embedding_column: str = "embedding", 
                        content_column: str = "content") -> SupabaseVectorStore:
        """
        获取 Langchain SupabaseVectorStore 实例，用于与 Langchain 生态系统集成。

        该方法返回一个配置好的 SupabaseVectorStore 实例，可以直接用于 Langchain 的各种操作，
        如文档检索、问答系统等。

        Args:
            table_name (str): 向量数据所在的表名。
            embedding_column (str, optional): 向量数据在表中的列名。默认为 "embedding"。
            content_column (str, optional): 文本内容在表中的列名。默认为 "content"。

        Returns:
            SupabaseVectorStore: 配置好的 Langchain SupabaseVectorStore 实例。

        Raises:
            VectorOperationError: 创建向量存储实例失败时抛出。
        """
        if not self.embedding_model:
            raise VectorOperationError("Embedding model is required for vector operations")
        
        try:
            # 创建 SupabaseVectorStore 实例
            vector_store = SupabaseVectorStore(
                client=self.supabase_client,
                embedding=self.embedding_model,
                table_name=table_name,
                query_name="match_documents",
                embedding_column=embedding_column,
                content_column=content_column
            )
            
            return vector_store
        
        except Exception as e:
            logging.error(f"Error creating vector store: {str(e)}")
            raise VectorOperationError(f"Failed to create vector store: {str(e)}")
    
    def add_documents_to_vector_store(self, documents: List[Document], table_name: str, 
                                     embedding_column: str = "embedding") -> List[str]:
        """
        将 Langchain Document 对象添加到向量存储中。

        该方法接收 Langchain Document 对象列表和其他参数作为输入，使用 SupabaseVectorStore 将文档添加到向量存储中。

        Args:
            documents (List[Document]): Langchain Document 对象列表。
            table_name (str): 向量数据所在的表名。
            embedding_column (str, optional): 向量数据在表中的列名。默认为 "embedding"。

        Returns:
            List[str]: 添加的文档 ID 列表。

        Raises:
            VectorOperationError: 添加文档失败时抛出。
        """
        try:
            # 获取向量存储实例
            vector_store = self.get_vector_store(table_name, embedding_column)
            
            # 添加文档
            doc_ids = vector_store.add_documents(documents)
            
            logging.info(f"Successfully added {len(documents)} documents to vector store")
            return doc_ids
        
        except Exception as e:
            logging.error(f"Error adding documents to vector store: {str(e)}")
            raise VectorOperationError(f"Failed to add documents to vector store: {str(e)}")
    
    def similarity_search(self, query: str, table_name: str, k: int = 4) -> List[Document]:
        """
        使用 Langchain 接口进行相似度搜索，返回 Document 对象。

        该方法接收查询文本和其他参数作为输入，使用 SupabaseVectorStore 进行相似度搜索，
        返回与查询文本最相似的 Document 对象列表。

        Args:
            query (str): 查询文本。
            table_name (str): 向量数据所在的表名。
            k (int, optional): 返回结果数量。默认为 4。

        Returns:
            List[Document]: 与查询文本最相似的 Document 对象列表。

        Raises:
            VectorOperationError: 相似度搜索失败时抛出。
        """
        try:
            # 获取向量存储实例
            vector_store = self.get_vector_store(table_name)
            
            # 执行相似度搜索
            docs = vector_store.similarity_search(query, k=k)
            
            logging.info(f"Successfully performed similarity search, found {len(docs)} documents")
            return docs
        
        except Exception as e:
            logging.error(f"Error performing similarity search: {str(e)}")
            raise VectorOperationError(f"Failed to perform similarity search: {str(e)}")
    
    # ==================== 辅助方法 ====================
    
    def _format_query_params(self, query_params: Dict[str, Any], query_builder: PostgrestFilterBuilder) -> PostgrestFilterBuilder:
        """
        根据查询参数格式化 Postgrest 查询构建器。

        该方法接收查询参数字典和 Postgrest 查询构建器作为输入，根据查询参数配置查询构建器，
        支持各种查询条件，如等于、大于、小于、模糊匹配等。

        Args:
            query_params (Dict[str, Any]): 查询参数字典。
            query_builder (PostgrestFilterBuilder): Postgrest 查询构建器。

        Returns:
            PostgrestFilterBuilder: 配置好的查询构建器。
        """
        # 处理选择字段
        if 'select' in query_params:
            query_builder = query_builder.select(query_params['select'])
        
        # 处理等于条件
        if 'eq' in query_params:
            for column, value in query_params['eq'].items():
                query_builder = query_builder.eq(column, value)
        
        # 处理大于条件
        if 'gt' in query_params:
            for column, value in query_params['gt'].items():
                query_builder = query_builder.gt(column, value)
        
        # 处理小于条件
        if 'lt' in query_params:
            for column, value in query_params['lt'].items():
                query_builder = query_builder.lt(column, value)
        
        # 处理大于等于条件
        if 'gte' in query_params:
            for column, value in query_params['gte'].items():
                query_builder = query_builder.gte(column, value)
        
        # 处理小于等于条件
        if 'lte' in query_params:
            for column, value in query_params['lte'].items():
                query_builder = query_builder.lte(column, value)
        
        # 处理模糊匹配条件
        if 'like' in query_params:
            for column, value in query_params['like'].items():
                query_builder = query_builder.like(column, value)
        
        # 处理排序
        if 'order' in query_params:
            column = query_params['order'].get('column')
            ascending = query_params['order'].get('ascending', True)
            if column:
                query_builder = query_builder.order(column, ascending=ascending)
        
        # 处理限制
        if 'limit' in query_params:
            query_builder = query_builder.limit(query_params['limit'])
        
        # 处理偏移
        if 'offset' in query_params:
            query_builder = query_builder.offset(query_params['offset'])
        
        return query_builder

    def _format_as_markdown(self, columns: List[str], rows: List[Any], title: Optional[str] = None) -> str:
        """
        将查询结果格式化为 Markdown 表格。

        Args:
            columns (List[str]): 列名列表。
            rows (List[Any]): 数据行列表。
            title (Optional[str]): 表格标题。

        Returns:
            str: Markdown 格式的表格字符串。
        """
        markdown = ""
        
        # 添加标题
        if title:
            markdown += f"# {title}\n\n"
        
        # 如果没有数据，返回提示信息
        if not rows:
            return markdown + "No data found.\n"
        
        # 添加表头
        markdown += "| " + " | ".join(columns) + " |\n"
        markdown += "| " + " | ".join(["---" for _ in columns]) + " |\n"
        
        # 添加数据行
        for row in rows:
            row_values = []
            for col_index, col_name in enumerate(columns):
                if isinstance(row, dict):
                    value = row.get(col_name, "")
                else:
                    value = row[col_index] if col_index < len(row) else ""
                
                # 处理特殊值，例如 None, 复杂对象等
                if value is None:
                    value = ""
                elif isinstance(value, (dict, list)):
                    value = json.dumps(value)
                
                # 转义竖线，防止破坏表格结构
                value = str(value).replace("|", "\\|")
                
                row_values.append(value)
            
            markdown += "| " + " | ".join(row_values) + " |\n"
        
        return markdown

    def query_similar_deals(self, query_text: str, match_threshold: float = 0.7, match_count: int = 10) -> List[Dict[str, Any]]:
        """
        查询与给定文本相似的交易。

        Args:
            query_text (str): 查询文本
            match_threshold (float): 相似度匹配阈值 (0-1)
            match_count (int): 返回的最大匹配数量

        Returns:
            List[Dict[str, Any]]: 相似交易列表

        Raises:
            VectorOperationError: 向量操作失败时抛出
        """
        try:
            if not query_text:
                return []
            
            if self.embedding_model and self._is_using_supabase():
                # 生成查询的嵌入向量
                query_embedding = self.embedding_model.embed_query(query_text)
                
                if self._is_using_supabase():
                    # 执行向量相似度查询 - Supabase (PostgreSQL) 版本
                    sql = """
                    WITH similarity_search AS (
                        SELECT
                            d.id,
                            d.name,
                            d.description,
                            d.price,
                            d.currency,
                            d.original_price,
                            d.discount_percentage,
                            d.url,
                            d.image_url,
                            d.product_id,
                            d.created_at,
                            d.updated_at,
                            d.expires_at,
                            d.deal_type,
                            d.source,
                            d.status,
                            d.embedding,
                            p.product_name,
                            p.brand,
                            p.category,
                            p.asin,
                            1 - (d.embedding <=> $1) as similarity
                        FROM
                            backend_deals d
                            LEFT JOIN
                                backend_products p ON d.product_id = p.id
                        WHERE
                            d.embedding IS NOT NULL
                            AND 1 - (d.embedding <=> $1) > $2
                        ORDER BY
                            similarity DESC
                        LIMIT $3
                    )
                    SELECT * FROM similarity_search
                    """
                    
                    params = {
                        "1": query_embedding,
                        "2": match_threshold,
                        "3": match_count
                    }
                    
                    result = self.execute_sql(sql, params)
                    
                    # 将相似度分数转换为百分比并四舍五入
                    for item in result:
                        if 'similarity' in item:
                            item['similarity'] = round(item['similarity'] * 100, 2)
                    
                    return result
                else:
                    # SQLite 版本 - 简化版，因为SQLite不原生支持向量操作
                    # 这里使用的是一个简化版本，实际使用中可能需要更复杂的实现或外部库支持
                    logging.warning("Vector similarity search is not fully supported in SQLite. Using simplified search.")
                    
                    sql = """
                    SELECT 
                        d.*,
                        p.product_name,
                        p.brand,
                        p.category,
                        p.asin
                    FROM 
                        backend_deals d
                    LEFT JOIN
                        backend_products p ON d.product_id = p.id
                    WHERE 
                        d.name LIKE ?
                    LIMIT ?
                    """
                    
                    # 从查询文本中提取关键词进行模糊匹配
                    keywords = ' '.join(['%' + word + '%' for word in query_text.split()[:3]])
                    params = (keywords, match_count)
            
            result = self.execute_sql(sql, params)
            logging.info(f"Found {len(result)} similar deals for query: {query_text[:50]}...")
            return result
        
        except Exception as e:
            logging.error(f"Error querying similar deals: {str(e)}")
            raise VectorOperationError(f"Failed to query similar deals: {str(e)}")

    def search_vectors(self, table_name: str, query_text: str, 
                     embedding_column: str = "embedding", 
                     content_column: str = "content",
                     match_threshold: float = 0.7, 
                     match_count: int = 10) -> List[Dict[str, Any]]:
        """
        在向量表中搜索与查询文本相似的记录。

        Args:
            table_name (str): 向量表名称
            query_text (str): 查询文本
            embedding_column (str): 存储向量嵌入的列名
            content_column (str): 存储文本内容的列名
            match_threshold (float): 相似度匹配阈值 (0-1)
            match_count (int): 返回的最大匹配数量

        Returns:
            List[Dict[str, Any]]: 相似记录列表

        Raises:
            VectorOperationError: 向量操作失败时抛出
        """
        if not self.embedding_model:
            raise VectorOperationError("Embedding model is required for vector operations")
        
        try:
            # 生成查询文本的嵌入向量
            query_embedding = self.embedding_model.embed_query(query_text)
            
            if self._is_using_supabase():
                # Supabase/PostgreSQL 向量搜索
                sql = f"""
                SELECT *, 1 - ({embedding_column} <=> $1) as similarity
                FROM {table_name}
                WHERE 1 - ({embedding_column} <=> $1) > $2
                ORDER BY similarity DESC
                LIMIT $3
                """
                
                params = {
                    "1": query_embedding,
                    "2": match_threshold,
                    "3": match_count
                }
                
                result = self.execute_sql(sql, params)
            else:
                # SQLite 简化版 - 基于文本内容的模糊匹配
                logging.warning("Vector similarity search is not fully supported in SQLite. Using text-based search.")
                
                sql = f"""
                SELECT * FROM {table_name}
                WHERE {content_column} LIKE ?
                LIMIT ?
                """
                
                # 从查询文本中提取关键词进行模糊匹配
                keywords = '%' + '%'.join(query_text.split()[:3]) + '%'
                result = self.execute_sql(sql, (keywords, match_count))
            
            logging.info(f"Found {len(result)} similar records in {table_name} for query: {query_text[:50]}...")
            return result
        
        except Exception as e:
            logging.error(f"Error searching vectors in {table_name}: {str(e)}")
            raise VectorOperationError(f"Failed to search vectors: {str(e)}")

    def get_row_by_id(self, table_name: str, row_id: int) -> Optional[Dict[str, Any]]:
        """
        通过ID获取单行数据的便捷方法。

        Args:
            table_name (str): 表名
            row_id (int): 行ID

        Returns:
            Optional[Dict[str, Any]]: 找到的行数据，如果未找到则返回None
        """
        try:
            query_params = {'eq': {'id': row_id}}
            result = self.fetch_data(table_name, query_params)
            return result[0] if result else None
        except Exception as e:
            logging.error(f"Error getting row by ID from {table_name}: {str(e)}")
            raise QueryError(f"Failed to get row by ID: {str(e)}")

    def count_rows(self, table_name: str, query_params: Optional[Dict[str, Any]] = None) -> int:
        """
        计算符合条件的行数。

        Args:
            table_name (str): 表名
            query_params (Dict[str, Any], optional): 查询条件

        Returns:
            int: 行数
        """
        try:
            if self._is_using_supabase():
                # Supabase 版本
                query = self.supabase_client.table(table_name).select('id', count='exact')
                
                # 处理过滤条件
                if query_params:
                    query = self._format_query_params(query_params, query)
                    
                response = query.execute()
                return response.count
            else:
                # SQLite 版本
                sql = f"SELECT COUNT(*) as count FROM {table_name}"
                where_clauses = []
                where_values = []
                
                if query_params:
                    # 等于条件
                    if 'eq' in query_params:
                        for column, value in query_params['eq'].items():
                            where_clauses.append(f"{column} = ?")
                            where_values.append(value)
                
                # 添加WHERE子句
                if where_clauses:
                    sql += " WHERE " + " AND ".join(where_clauses)
                
                result = self.execute_sql(sql, tuple(where_values))
                return result[0]['count'] if result else 0
        except Exception as e:
            logging.error(f"Error counting rows in {table_name}: {str(e)}")
            raise QueryError(f"Failed to count rows: {str(e)}")

    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在。

        Args:
            table_name (str): 表名

        Returns:
            bool: 表是否存在
        """
        try:
            if self._is_using_supabase():
                # 使用Supabase/PostgreSQL元数据查询
                sql = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public'
                    AND table_name = $1
                ) as exists
                """
                result = self.execute_sql(sql, (table_name,))
                return result[0]['exists'] if result else False
            else:
                # SQLite版本
                sql = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
                result = self.execute_sql(sql, (table_name,))
                return len(result) > 0
        except Exception as e:
            logging.error(f"Error checking if table {table_name} exists: {str(e)}")
            raise QueryError(f"Failed to check if table exists: {str(e)}")

    def backup_database(self, backup_path: Optional[str] = None) -> str:
        """
        备份数据库。

        Args:
            backup_path (str, optional): 备份文件路径。如果未提供，则使用默认路径。

        Returns:
            str: 备份文件路径
        """
        try:
            if hasattr(self, 'sqlite_conn') and self.sqlite_conn is not None:
                # SQLite备份
                import shutil
                import datetime
                
                # 生成备份文件路径
                if not backup_path:
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_dir = os.path.dirname(self.sqlite_path)
                    backup_filename = f"{os.path.basename(self.sqlite_path)}.{timestamp}.bak"
                    backup_path = os.path.join(backup_dir, backup_filename)
                
                # 确保目标目录存在
                os.makedirs(os.path.dirname(backup_path), exist_ok=True)
                
                # 备份数据库文件
                shutil.copy2(self.sqlite_path, backup_path)
                logging.info(f"Successfully backed up SQLite database to {backup_path}")
                return backup_path
            else:
                # Supabase不直接支持备份，记录警告
                logging.warning("Database backup is not directly supported for Supabase connections")
                return "Backup not supported for Supabase"
        except Exception as e:
            logging.error(f"Error backing up database: {str(e)}")
            raise DBConnectorError(f"Failed to backup database: {str(e)}")

    # 首先添加异步连接管理
    async def _init_sqlite_async(self):
        """初始化异步SQLite连接池"""
        if not hasattr(self, '_async_connection_pool'):
            self._async_connection_pool = {}
            self._global_sqlite_lock = asyncio.Lock()
        
        if not hasattr(self, '_async_connection_count'):
            self._async_connection_count = 0
        
        if not hasattr(self, 'logger'):
            self.logger = logging.getLogger(__name__)

    async def get_async_connection(self):
        """获取异步SQLite连接"""
        task_id = id(asyncio.current_task())
        
        if task_id in self._async_connection_pool and self._async_connection_pool[task_id] is not None:
            return self._async_connection_pool[task_id]
        
        # 创建新连接
        connection = await aiosqlite.connect(self.sqlite_path)
        connection.row_factory = aiosqlite.Row
        
        # 存储连接到池中
        self._async_connection_pool[task_id] = connection
        self._async_connection_count += 1
        self.logger.debug(f"New async connection created. Total: {self._async_connection_count}")
        
        return connection

    async def execute_sql_async(self, sql: str, params: Any = None) -> List[Dict[str, Any]]:
        """
        异步执行SQL查询。
        
        Args:
            sql (str): SQL查询语句
            params (Any, optional): 查询参数
            
        Returns:
            List[Dict[str, Any]]: 查询结果
            
        Raises:
            QueryError: SQL执行失败时抛出
        """
        try:
            # 获取异步连接
            await self._init_sqlite_async()
            conn = await self.get_async_connection()
            
            async with conn.execute(sql, params or ()) as cursor:
                if sql.strip().upper().startswith(('SELECT', 'WITH')):
                    # 查询操作
                    rows = await cursor.fetchall()
                    if cursor.description:
                        columns = [column[0] for column in cursor.description]
                        result = []
                        for row in rows:
                            if isinstance(row, tuple):
                                # 如果是元组，转换为字典
                                row_dict = {columns[i]: value for i, value in enumerate(row) if i < len(columns)}
                                result.append(row_dict)
                            else:
                                # 如果已经是字典，直接添加
                                result.append(dict(row))
                    return result
                else:
                    # 非查询操作
                    await conn.commit()
                    return [{"affected_rows": cursor.rowcount}]
        except Exception as e:
            self.logger.error(f"Error executing SQL asynchronously: {str(e)}")
            raise QueryError(f"Failed to execute SQL asynchronously: {str(e)}")

    async def insert_data_async(self, table_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        异步版本：向指定的表中插入一条数据。
        
        Args:
            table_name (str): 要插入数据的表名。
            data (Dict[str, Any]): 要插入的数据，例如 {'name': 'John', 'age': 30}。
            
        Returns:
            Dict[str, Any]: 插入的数据，包含自动生成的ID。
            
        Raises:
            InsertError: 插入数据失败时抛出。
        """
        try:
            if self._is_using_supabase():
                # Supabase 目前没有官方的异步支持，使用同步方法
                return self.insert_data(table_name, data)
            else:
                # SQLite 异步插入
                # 获取异步连接
                await self._init_sqlite_async()
                conn = await self.get_async_connection()
                
                # 构建 SQL 语句
                columns = list(data.keys())
                placeholders = ', '.join(['?'] * len(columns))
                values = [data[col] for col in columns]
                
                sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
                
                # 执行插入
                async with conn.execute(sql, values) as cursor:
                    # 获取最后插入的行ID
                    last_id = cursor.lastrowid
                
                # 提交更改
                await conn.commit()
                
                # 查询插入的数据
                result_sql = f"SELECT * FROM {table_name} WHERE id = ?"
                async with conn.execute(result_sql, (last_id,)) as cursor:
                    row = await cursor.fetchone()
                    
                    # 返回插入的数据
                    if row:
                        inserted_data = dict(row)
                        self.logger.info(f"Successfully inserted data into {table_name} asynchronously")
                        return inserted_data
                    else:
                        # 返回原始数据加上ID
                        data_with_id = {**data, "id": last_id}
                        self.logger.info(f"Successfully inserted data into {table_name} asynchronously")
                        return data_with_id
        except Exception as e:
            self.logger.error(f"Error inserting data into {table_name} asynchronously: {str(e)}")
            raise InsertError(f"Failed to insert data into {table_name} asynchronously: {str(e)}")

    async def batch_insert_data_async(self, table_name: str, data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        异步版本：向指定的表中批量插入多条数据。
        
        Args:
            table_name (str): 要插入数据的表名。
            data_list (List[Dict[str, Any]]): 要插入的数据列表。
            
        Returns:
            List[Dict[str, Any]]: 插入的数据列表，包含自动生成的ID。
            
        Raises:
            InsertError: 插入数据失败时抛出。
        """
        if not data_list:
            return []
        
        try:
            if self._is_using_supabase():
                # Supabase 目前没有官方的异步支持，使用同步方法
                return self.batch_insert_data(table_name, data_list)
            else:
                # SQLite 异步批量插入
                # 获取异步连接
                await self._init_sqlite_async()
                conn = await self.get_async_connection()
                
                # 开始事务
                await conn.execute("BEGIN TRANSACTION")
                
                try:
                    # 准备插入
                    results = []
                    for data in data_list:
                        # 构建 SQL 语句
                        columns = list(data.keys())
                        placeholders = ', '.join(['?'] * len(columns))
                        values = [data[col] for col in columns]
                        
                        sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
                        
                        # 执行插入
                        async with conn.execute(sql, values) as cursor:
                            # 获取最后插入的行ID
                            last_id = cursor.lastrowid
                        
                        # 查询插入的数据
                        result_sql = f"SELECT * FROM {table_name} WHERE id = ?"
                        async with conn.execute(result_sql, (last_id,)) as cursor:
                            row = await cursor.fetchone()
                            
                            # 添加到结果列表
                            if row:
                                results.append(dict(row))
                            else:
                                # 返回原始数据加上ID
                                data_with_id = {**data, "id": last_id}
                                results.append(data_with_id)
                    
                    # 提交事务
                    await conn.commit()
                    self.logger.info(f"Successfully batch inserted {len(data_list)} records into {table_name} asynchronously")
                    return results
                    
                except Exception as e:
                    # 回滚事务
                    await conn.rollback()
                    raise e
        except Exception as e:
            if isinstance(e, InsertError):
                raise
            self.logger.error(f"Error batch inserting data into {table_name} asynchronously: {str(e)}")
            raise InsertError(f"Failed to batch insert data into {table_name} asynchronously: {str(e)}")

    async def update_data_async(self, table_name: str, update_data: Dict[str, Any], filter_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        异步版本：更新表中符合条件的数据。
        
        Args:
            table_name (str): 要更新的表名。
            update_data (Dict[str, Any]): 要更新的数据，例如 {'name': 'new_name', 'status': 'active'}。
            filter_params (Dict[str, Any]): 过滤条件，可以是直接条件如 {'id': 123} 或嵌套条件如 {'eq': {'id': 123}}。
            
        Returns:
            Dict[str, Any]: 更新后的数据。
            
        Raises:
            UpdateError: 更新数据失败时抛出。
        """
        if not update_data:
            return {}
        
        try:
            # 处理嵌套过滤条件
            actual_filter = {}
            if 'eq' in filter_params and isinstance(filter_params['eq'], dict):
                actual_filter = filter_params['eq']
            else:
                actual_filter = filter_params
            
            if self._is_using_supabase():
                # Supabase 目前没有官方的异步支持，使用同步方法
                return self.update_data(table_name, update_data, filter_params)
            else:
                # SQLite 异步更新
                # 获取异步连接
                await self._init_sqlite_async()
                conn = await self.get_async_connection()
                
                # 构建 SET 子句
                set_clauses = []
                set_values = []
                for key, value in update_data.items():
                    set_clauses.append(f"{key} = ?")
                    set_values.append(value)
                
                # 构建 WHERE 子句
                where_clauses = []
                where_values = []
                for key, value in actual_filter.items():
                    where_clauses.append(f"{key} = ?")
                    where_values.append(value)
                
                # 构建完整 SQL
                sql = f"UPDATE {table_name} SET {', '.join(set_clauses)} WHERE {' AND '.join(where_clauses)}"
                params = set_values + where_values
                
                # 执行更新
                async with conn.execute(sql, params) as cursor:
                    affected_rows = cursor.rowcount
                
                # 提交更改
                await conn.commit()
                
                if affected_rows > 0:
                    # 查询更新后的数据
                    result_sql = f"SELECT * FROM {table_name} WHERE {' AND '.join(where_clauses)}"
                    async with conn.execute(result_sql, where_values) as cursor:
                        row = await cursor.fetchone()
                        
                        if row:
                            self.logger.info(f"Successfully updated data in {table_name} asynchronously")
                            return dict(row)
                
                # 返回空结果
                return {}
        except Exception as e:
            if isinstance(e, UpdateError):
                raise
            self.logger.error(f"Error updating data in {table_name} asynchronously: {str(e)}")
            raise UpdateError(f"Failed to update data in {table_name} asynchronously: {str(e)}")

    async def delete_data_async(self, table_name: str, filter_params: Dict[str, Any]) -> int:
        """
        异步版本：从指定的表中删除符合条件的数据。
        
        Args:
            table_name (str): 要删除数据的表名。
            filter_params (Dict[str, Any]): 过滤条件，可以是直接条件如 {'id': 123} 或嵌套条件如 {'eq': {'id': 123}}。
            
        Returns:
            int: 删除的记录数量。
            
        Raises:
            DeleteError: 删除数据失败时抛出。
        """
        try:
            # 处理嵌套过滤条件
            actual_filter = {}
            if 'eq' in filter_params and isinstance(filter_params['eq'], dict):
                actual_filter = filter_params['eq']
            else:
                actual_filter = filter_params
            
            if self._is_using_supabase():
                # Supabase 目前没有官方的异步支持，使用同步方法
                return self.delete_data(table_name, filter_params)
            else:
                # SQLite 异步删除
                # 获取异步连接
                await self._init_sqlite_async()
                conn = await self.get_async_connection()
                
                # 构建 WHERE 子句
                where_clauses = []
                where_values = []
                for key, value in actual_filter.items():
                    where_clauses.append(f"{key} = ?")
                    where_values.append(value)
                
                # 构建完整 SQL
                sql = f"DELETE FROM {table_name} WHERE {' AND '.join(where_clauses)}"
                
                # 执行删除
                async with conn.execute(sql, where_values) as cursor:
                    affected_rows = cursor.rowcount
                
                # 提交更改
                await conn.commit()
                
                self.logger.info(f"Successfully deleted {affected_rows} records from {table_name} asynchronously")
                return affected_rows
        except Exception as e:
            if isinstance(e, DeleteError):
                raise
            self.logger.error(f"Error deleting data from {table_name} asynchronously: {str(e)}")
            raise DeleteError(f"Failed to delete data from {table_name} asynchronously: {str(e)}")

    async def execute_transaction_async(self, sql_statements: List[Tuple[str, Any]]) -> List[Dict[str, Any]]:
        """
        异步执行事务，包含多个SQL语句。
        
        Args:
            sql_statements (List[Tuple[str, Any]]): 要执行的SQL语句列表，每个元素是(sql, params)元组
            
        Returns:
            List[Dict[str, Any]]: 最后一个查询的结果
        """
        # 创建一个信号量来限制并发访问
        await self._init_sqlite_async()
        
        # 使用全局锁确保一次只有一个事务可以访问数据库
        async with self._global_sqlite_lock:
            # 为每个事务创建一个新的连接，避免事务嵌套问题
            async with aiosqlite.connect(self.sqlite_path) as conn:
                conn.row_factory = aiosqlite.Row
                
                try:
                    # 设置超时和隔离级别
                    await conn.execute("PRAGMA busy_timeout = 5000")  # 5秒超时
                    await conn.execute("PRAGMA journal_mode = WAL")  # 使用WAL模式提高并发性
                    await conn.execute("PRAGMA synchronous = NORMAL")  # 适当降低同步级别提高性能
                    
                    # 开始事务
                    await conn.execute("BEGIN IMMEDIATE")  # 使用IMMEDIATE减少死锁
                    
                    result = None
                    for sql, params in sql_statements:
                        async with conn.execute(sql, params or ()) as cursor:
                            if sql.strip().upper().startswith(('SELECT', 'WITH')):
                                # 如果是查询语句，获取结果
                                rows = await cursor.fetchall()
                                if cursor.description:
                                    columns = [column[0] for column in cursor.description]
                                    result = []
                                    for row in rows:
                                        if isinstance(row, tuple):
                                            # 如果是元组，转换为字典
                                            row_dict = {columns[i]: value for i, value in enumerate(row) if i < len(columns)}
                                            result.append(row_dict)
                                        else:
                                            # 如果已经是字典，直接添加
                                            result.append(dict(row))
                
                    # 提交事务
                    await conn.commit()
                    return result or []
                    
                except Exception as e:
                    # 发生异常，回滚事务
                    await conn.rollback()
                    self.logger.error(f"Error executing transaction asynchronously: {str(e)}")
                    raise QueryError(f"Failed to execute transaction asynchronously: {str(e)}")

def create_db_connector(supabase_url: str, supabase_key: str, embedding_model: Optional[Embeddings] = None) -> 'DBConnector':
    """
    工厂函数，用于创建 DBConnector 实例。

    Args:
        supabase_url (str): Supabase 项目 URL.
        supabase_key (str): Supabase API 密钥.
        embedding_model (Optional[Embeddings]): 用于生成向量嵌入的模型。

    Returns:
        DBConnector: 创建的 DBConnector 实例.
    """
    return DBConnector(supabase_url=supabase_url, supabase_key=supabase_key, embedding_model=embedding_model)

def create_sqlite_connector(sqlite_path: str, embedding_model: Optional[Embeddings] = None) -> 'DBConnector':
    """
    工厂函数，用于创建使用SQLite的DBConnector实例。

    Args:
        sqlite_path (str): SQLite数据库文件路径
        embedding_model (Optional[Embeddings]): 用于生成向量嵌入的模型

    Returns:
        DBConnector: 创建的DBConnector实例
    """
    return DBConnector(sqlite_path=sqlite_path, embedding_model=embedding_model)

if __name__ == '__main__':
    """
    单元测试: 测试 DBConnector 的各项功能
    """
    import asyncio
    import os
    import sys
    import time
    import random
    import uuid
    import traceback
    from datetime import datetime

    # 配置日志
    logging.basicConfig(level=logging.INFO, 
                        format='%(levelname)s:%(name)s:%(message)s')
    
    # 创建测试用的 SQLite 数据库
    test_db_path = "test_database.db"
    db = create_sqlite_connector(test_db_path)
    
    # 辅助函数: 创建测试表
    @staticmethod
    def setup_test_tables():
        """设置测试表和初始数据"""
        db = create_sqlite_connector(':memory:')
        
        # 创建用户测试表
        db.execute_sql("""
        CREATE TABLE IF NOT EXISTS backend_test_users (
            id INTEGER PRIMARY KEY,
            name TEXT,
            email TEXT UNIQUE,
            age INTEGER,
            created_at TEXT
        )
        """)
        
        # 创建计数器测试表
        db.execute_sql("""
        CREATE TABLE IF NOT EXISTS backend_test_counter (
            id INTEGER PRIMARY KEY,
            count INTEGER DEFAULT 0
        )
        """)
        
        # 检查计数器表中是否已有记录
        counter_exists = db.execute_sql("SELECT COUNT(*) as count FROM backend_test_counter WHERE id = 1")
        
        # 只有在不存在时才插入初始计数器
        if counter_exists[0]['count'] == 0:
            db.execute_sql("INSERT INTO backend_test_counter (id, count) VALUES (1, 0)")
        else:
            # 如果已存在，重置计数器为0
            db.execute_sql("UPDATE backend_test_counter SET count = 0 WHERE id = 1")
            
        print("测试表设置完成")
        return db
    
    # 清理函数: 删除测试数据
    def cleanup_test_data():
        print("\n=== 清理测试数据 ===")
        try:
            db.execute_sql("DELETE FROM backend_test_users")
            db.execute_sql("UPDATE backend_test_counter SET count = 0")
            print("测试数据已清理")
        except Exception as e:
            print(f"清理数据时出错: {str(e)}")
    
    # 测试: 基本 CRUD 操作
    def test_basic_crud():
        print("\n=== 测试基本 CRUD 操作 ===")
        
        # 插入数据
        test_user = {
            "name": "Test User",
            "email": f"test{random.randint(1000, 9999)}@example.com",
            "age": 30
        }
        insert_result = db.insert_data("backend_test_users", test_user)
        print(f"插入结果: {insert_result}")
        
        # 查询数据
        query_result = db.fetch_data("backend_test_users", {"eq": {"name": "Test User"}})
        print(f"查询结果: {query_result}")
        
        if query_result:
            user_id = query_result[0]['id']
            
            # 更新数据
            update_data = {"age": 31}
            update_result = db.update_data("backend_test_users", update_data, {"eq": {"id": user_id}})
            print(f"更新结果: {update_result}")
            
            # 验证更新
            updated_user = db.fetch_data("backend_test_users", {"eq": {"id": user_id}})
            print(f"更新后的用户: {updated_user}")
            assert updated_user[0]["age"] == 31, "更新操作失败"
            
            # 删除数据
            delete_result = db.delete_data("backend_test_users", {"eq": {"id": user_id}})
            print(f"删除结果: {delete_result}")
            
            # 验证删除
            deleted_check = db.fetch_data("backend_test_users", {"eq": {"id": user_id}})
            assert len(deleted_check) == 0, "删除操作失败"
        
        print("基本 CRUD 测试通过")
    
    # 测试: SQL 执行
    def test_sql_execution():
        print("\n=== 测试 SQL 执行 ===")
        
        # 生成唯一用户名，避免与之前的测试数据冲突
        unique_id = uuid.uuid4().hex[:8]
        unique_user_name = f"SQL User {unique_id}"
        
        # 直接执行 SQL 插入
        sql = "INSERT INTO backend_test_users (name, email, age) VALUES (?, ?, ?)"
        params = (unique_user_name, f"sql{random.randint(1000, 9999)}@example.com", 40)
        
        result = db.execute_sql(sql, params)
        print(f"SQL 执行结果: {result}")
        
        # 验证插入 - 使用精确的用户名查询
        query_result = db.fetch_data("backend_test_users", {"eq": {"name": unique_user_name}})
        print(f"SQL 插入的用户: {query_result}")
        assert len(query_result) == 1, f"SQL 插入操作失败: 未找到唯一用户 '{unique_user_name}'"
        
        print("SQL 执行测试通过")
    
    # 测试: 事务执行
    def test_transaction():
        print("\n=== 测试事务操作 ===")
        
        # 创建一个事务
        transaction_statements = [
            (
                "INSERT INTO backend_test_users (name, email, age) VALUES (?, ?, ?)",
                ("Transaction User", f"transaction{random.randint(1000, 9999)}@example.com", 40)
            ),
            (
                "UPDATE backend_test_counter SET count = count + 1 WHERE id = 1",
                ()
            ),
            (
                "SELECT count FROM backend_test_counter WHERE id = 1",
                ()
            )
        ]
        
        try:
            transaction_result = db.execute_transaction(transaction_statements)
            print(f"事务执行结果: {transaction_result}")
            
            # 验证事务结果
            counter_result = db.execute_sql("SELECT count FROM backend_test_counter WHERE id = 1")
            print(f"计数器值: {counter_result}")
            
            # 验证用户插入
            user_result = db.fetch_data("backend_test_users", {"eq": {"name": "Transaction User"}})
            print(f"事务插入的用户: {user_result}")
            
            print("事务测试通过")
        except Exception as e:
            print(f"测试过程中出错: {str(e)}")
    
    # 测试: 并发处理
    def test_concurrency():
        print("\n=== 测试并发处理 ===")
        # 重置计数器
        db.execute_sql("UPDATE backend_test_counter SET count = 0 WHERE id = 1")
        
        # 创建多个工作器
        num_workers = 5
        ops_per_worker = 20
        print(f"启动 {num_workers} 个工作器，每个执行 {ops_per_worker} 次操作")
        
        # 定义工作器函数
        def worker(worker_id):
            print(f"工作器 {worker_id} 开始运行")
            success_count = 0
            fail_count = 0
            
            for i in range(ops_per_worker):
                try:
                    # 增加计数器值
                    sql = "UPDATE backend_test_counter SET count = count + 1 WHERE id = 1"
                    db.execute_sql(sql)
                    success_count += 1
                except Exception as e:
                    fail_count += 1
                    print(f"工作器 {worker_id} 操作 {i} 失败: {str(e)}")
            
            print(f"工作器 {worker_id} 完成: 成功 {success_count}, 失败 {fail_count}")
            return success_count, fail_count
        
        # 启动所有工作器
        workers = []
        for i in range(num_workers):
            workers.append(worker(i))
        
        # 汇总结果
        results = workers
        total_success = sum(s for s, f in results)
        total_fail = sum(f for s, f in results)
        
        # 检查最终结果
        counter_result = db.execute_sql("SELECT count FROM backend_test_counter WHERE id = 1")
        if isinstance(counter_result[0], dict):
            final_count = counter_result[0]['count']
        else:
            final_count = counter_result[0][0]
        
        print(f"所有工作器完成: 成功 {total_success}, 失败 {total_fail}")
        print(f"最终计数器值: {final_count}")
        
        # 验证结果
        # 注意：在高并发环境下，由于SQLite的锁定机制，最终计数可能不等于成功操作数
        # 但在低并发或单线程环境中应该相等
        print(f"计数比较: 成功操作数 {total_success}, 最终计数器值 {final_count}")
        print("并发测试完成")
    
    # 异步测试函数
    async def test_async_sqlite_operations():
        print("\n=== 测试异步SQLite操作 ===")
        
        # 测试异步SQL执行
        print("测试异步SQL执行...")
        async_result = await db.execute_sql_async("SELECT * FROM backend_test_counter")
        print(f"异步SQL执行结果: {async_result}")
        
        # 测试异步插入
        print("\n测试异步插入...")
        test_user = {
            "name": f"Async User {uuid.uuid4().hex[:8]}",
            "email": f"async{uuid.uuid4().hex[:8]}@example.com",
            "age": 25
        }
        insert_result = await db.insert_data_async("backend_test_users", test_user)
        print(f"异步插入结果: {insert_result}")
        
        # 测试异步事务
        print("\n测试异步事务...")
        transaction_statements = [
            (
                "INSERT INTO backend_test_users (name, email, age) VALUES (?, ?, ?)",
                (f"Async Transaction User {uuid.uuid4().hex[:8]}", f"async_tx{uuid.uuid4().hex[:8]}@example.com", 35)
            ),
            (
                "UPDATE backend_test_counter SET count = count + 1 WHERE id = 1",
                ()
            ),
            (
                "SELECT count FROM backend_test_counter WHERE id = 1",
                ()
            )
        ]
        
        try:
            transaction_result = await db.execute_transaction_async(transaction_statements)
            print(f"异步事务执行结果: {transaction_result}")
        except Exception as e:
            print(f"异步事务执行失败: {str(e)}")
            traceback.print_exc()
        
        # 测试异步批量插入
        print("\n测试异步批量插入...")
        users = [
            {
                "name": f"Async Batch User {i}",
                "email": f"async_batch{i}_{uuid.uuid4().hex[:8]}@example.com",
                "age": 20 + i
            }
            for i in range(3)
        ]
        
        try:
            batch_result = await db.batch_insert_data_async("backend_test_users", users)
            print(f"异步批量插入结果: {batch_result}")
        except Exception as e:
            print(f"异步批量插入失败: {str(e)}")
        
        print("异步SQLite测试完成")
    
    # 测试改进的并发处理
    async def test_improved_concurrency():
        print("\n=== 测试改进的并发处理 ===")
        
        # 重置计数器
        await db.execute_sql_async("UPDATE backend_test_counter SET count = 0 WHERE id = 1")
        
        # 创建多个工作器
        num_workers = 3
        ops_per_worker = 5
        print(f"使用改进的并发处理启动 {num_workers} 个工作器，每个执行 {ops_per_worker} 次操作")
        
        # 定义工作器函数
        async def worker(worker_id):
            print(f"工作器 {worker_id} 开始运行，将执行 {ops_per_worker} 次操作")
            success_count = 0
            fail_count = 0
            
            for i in range(ops_per_worker):
                try:
                    # 使用单条SQL语句而不是事务，减少锁定问题
                    await db.execute_sql_async("UPDATE backend_test_counter SET count = count + 1 WHERE id = 1")
                    success_count += 1
                    # 添加短暂的延迟，减少冲突
                    await asyncio.sleep(0.01)
                except Exception as e:
                    fail_count += 1
                    print(f"工作器 {worker_id} 操作 {i} 失败: {str(e)}")
            
            print(f"工作器 {worker_id} 完成: 成功 {success_count}, 失败 {fail_count}")
            return success_count, fail_count
        
        # 创建所有工作器的任务
        tasks = [worker(i) for i in range(num_workers)]
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks)
        
        total_success = sum(s for s, f in results)
        total_fail = sum(f for s, f in results)
        
        # 检查最终结果
        counter_result = await db.execute_sql_async("SELECT count FROM backend_test_counter WHERE id = 1")
        
        # 更确保地处理结果格式
        final_count = 0
        if counter_result and len(counter_result) > 0:
            if isinstance(counter_result[0], dict) and 'count' in counter_result[0]:
                final_count = counter_result[0]['count']
            elif isinstance(counter_result[0], (list, tuple)) and len(counter_result[0]) > 0:
                # 如果是列表或元组，尝试获取第一个元素
                final_count = counter_result[0][0]
            elif hasattr(counter_result[0], 'count'):
                # 如果是具有count属性的对象
                final_count = counter_result[0].count
            else:
                # 直接尝试将第一个结果作为计数
                try:
                    final_count = int(counter_result[0])
                except (ValueError, TypeError):
                    print(f"无法解析计数器结果: {counter_result}")
        else:
            print("警告: 计数器查询没有返回结果")
        
        print(f"所有工作器完成: 成功 {total_success}, 失败 {total_fail}")
        print(f"最终计数器值: {final_count}")
        
        # 验证结果 - 添加更多容错性
        if final_count != total_success:
            print(f"警告: 计数器值 {final_count} 与成功操作数 {total_success} 不匹配")
            print("这在SQLite的并发环境中可能是正常的")
        else:
            print("并发测试通过: 计数器值与成功操作数匹配")
    
    # 主测试流程
    try:
        # 设置测试表
        setup_test_tables()
        
        # 运行同步测试
        test_basic_crud()
        test_sql_execution()
        test_transaction()
        test_concurrency()
        
        # 运行异步测试
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
        
        # 使用asyncio.run运行所有异步测试
        asyncio.run(test_async_sqlite_operations())
        asyncio.run(test_improved_concurrency())
        
        # 清理测试数据
        cleanup_test_data()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        traceback.print_exc()
        
    finally:
        # 关闭数据库连接
        if hasattr(db, 'sqlite_conn') and db.sqlite_conn:
            db.sqlite_conn.close()
        print("测试结束，数据库连接已关闭")