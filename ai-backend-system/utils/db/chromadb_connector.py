# ai-backend-system/utils/db/chromadb_connector.py
"""
定义 ChromaDBConnector 类。

该 Util 负责连接和操作 Chroma DB 向量数据库，提供统一的接口与向量数据库进行交互。
作为 Human-agents block 与向量数据库交互的桥梁。

核心功能包括：
- 连接 Chroma DB 数据库 (支持持久化存储)
- 创建、获取、删除 Collection
- 向 Collection 插入数据 (add)
- 从 Collection 获取数据 (get)
- 查询 Collection，进行向量相似度检索 (query)
- 更新 Collection 数据 (update)
- 删除 Collection 数据 (delete)
"""
import os
import logging
from typing import List, Dict, Any, Optional, Union, Tuple

# Import Chroma DB
try:
    import chromadb
    from chromadb.api.models.Collection import Collection
    from chromadb.types import Include
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    # Create dummy classes to avoid errors
    class Collection:
        pass
    class Include:
        METADATAS = "metadatas"
        DOCUMENTS = "documents"
        DISTANCE = "distances"
        EMBEDDINGS = "embeddings"

# Langchain Integration (Optional, if needed)
# from langchain_core.embeddings import Embeddings
# from langchain_core.documents import Document
# from langchain_community.vectorstores import Chroma

# 自定义异常
class ChromaDBConnectorError(Exception):
    """ChromaDBConnector 相关异常的基类"""
    pass

class ConnectionError(ChromaDBConnectorError):
    """数据库连接错误"""
    pass

class CollectionError(ChromaDBConnectorError):
    """Collection 操作错误"""
    pass

class OperationError(ChromaDBConnectorError):
    """数据操作错误 (插入、更新、删除、查询)"""
    pass

class ChromaDBConnector:
    """
    ChromaDBConnector, 负责连接和操作 Chroma DB 向量数据库。
    作为 Human-agents block 与向量数据库交互的桥梁。
    """
    def __init__(self,
                 path: Optional[str] = None,
                 host: Optional[str] = None,
                 port: Optional[int] = None,
                 # embedding_model: Optional[Embeddings] = None # Optional: for Langchain integration
                 ):
        """
        初始化 ChromaDBConnector 实例。

        Args:
            path (Optional[str]): Chroma DB 持久化存储路径，例如 "./chroma_db"。
                                  如果未提供，将使用内存存储。
            host (Optional[str]): Chroma DB 服务器地址 (用于客户端模式)。
            port (Optional[int]): Chroma DB 服务器端口 (用于客户端模式)。
            # embedding_model (Optional[Embeddings]): 用于生成向量嵌入的模型 (用于 Langchain 集成)。

        Raises:
            ConnectionError: 连接数据库失败时抛出。
            ImportError: 如果 chromadb 库未安装。
        """
        #if not CHROMADB_AVAILABLE:
        #    raise ImportError("The 'chromadb' library is not installed. Please install it with 'pip install chromadb'.")

        self.path = path
        self.host = host
        self.port = port
        # self.embedding_model = embedding_model # Optional
        self.client = None
        self.logger = logging.getLogger(__name__)

        self._init_client()

    def _init_client(self):
        """初始化 Chroma DB 客户端"""
        try:
            if self.host and self.port:
                # 连接到远程 Chroma DB 服务器
                self.client = chromadb.HttpClient(host=self.host, port=self.port)
                self.logger.info(f"Successfully connected to Chroma DB server at {self.host}:{self.port}")
            elif self.path:
                # 使用持久化存储
                self.client = chromadb.PersistentClient(path=self.path)
                self.logger.info(f"Successfully initialized Chroma DB persistent client at {self.path}")
            else:
                # 使用内存存储
                self.client = chromadb.Client()
                self.logger.info("Successfully initialized Chroma DB in-memory client")

            # Optional: Test connection by listing collections
            # self.client.list_collections()
            # self.logger.info("Chroma DB client connection tested successfully.")

        except Exception as e:
            self.logger.error(f"Failed to connect to Chroma DB: {str(e)}")
            raise ConnectionError(f"Failed to connect to Chroma DB: {str(e)}")

    # ==================== Collection 管理接口 ====================

    def create_collection(self, name: str, metadata: Optional[Dict[str, Any]] = None) -> Collection:
        """
        创建新的 Collection。

        Args:
            name (str): Collection 名称。
            metadata (Optional[Dict[str, Any]]): Collection 的元数据。

        Returns:
            Collection: 创建的 Collection 实例。

        Raises:
            CollectionError: 创建 Collection 失败时抛出。
        """
        try:
            collection = self.client.create_collection(name=name, metadata=metadata)
            self.logger.info(f"Successfully created collection: {name}")
            return collection
        except Exception as e:
            self.logger.error(f"Error creating collection {name}: {str(e)}")
            raise CollectionError(f"Failed to create collection {name}: {str(e)}")

    def get_collection(self, name: str) -> Collection:
        """
        获取现有 Collection。

        Args:
            name (str): Collection 名称。

        Returns:
            Collection: 获取的 Collection 实例。

        Raises:
            CollectionError: 获取 Collection 失败时抛出。
        """
        try:
            collection = self.client.get_collection(name=name)
            self.logger.info(f"Successfully got collection: {name}")
            return collection
        except Exception as e:
            self.logger.error(f"Error getting collection {name}: {str(e)}")
            raise CollectionError(f"Failed to get collection {name}: {str(e)}")

    def get_or_create_collection(self, name: str, metadata: Optional[Dict[str, Any]] = None) -> Collection:
        """
        获取现有 Collection，如果不存在则创建。

        Args:
            name (str): Collection 名称。
            metadata (Optional[Dict[str, Any]]): Collection 的元数据 (仅在创建时使用)。

        Returns:
            Collection: 获取或创建的 Collection 实例。

        Raises:
            CollectionError: 操作失败时抛出。
        """
        try:
            collection = self.client.get_or_create_collection(name=name, metadata=metadata)
            self.logger.info(f"Successfully got or created collection: {name}")
            return collection
        except Exception as e:
            self.logger.error(f"Error getting or creating collection {name}: {str(e)}")
            raise CollectionError(f"Failed to get or create collection {name}: {str(e)}")

    def delete_collection(self, name: str):
        """
        删除 Collection。

        Args:
            name (str): Collection 名称。

        Raises:
            CollectionError: 删除 Collection 失败时抛出。
        """
        try:
            self.client.delete_collection(name=name)
            self.logger.info(f"Successfully deleted collection: {name}")
        except Exception as e:
            self.logger.error(f"Error deleting collection {name}: {str(e)}")
            raise CollectionError(f"Failed to delete collection {name}: {str(e)}")

    def list_collections(self) -> List[Collection]:
        """
        列出所有 Collection。

        Returns:
            List[Collection]: Collection 实例列表。

        Raises:
            CollectionError: 列出 Collection 失败时抛出。
        """
        try:
            collections = self.client.list_collections()
            self.logger.info(f"Successfully listed {len(collections)} collections")
            return collections
        except Exception as e:
            self.logger.error(f"Error listing collections: {str(e)}")
            raise CollectionError(f"Failed to list collections: {str(e)}")

    # ==================== 数据操作接口 ====================

    def add_data(self, collection_name: str, documents: List[str],
                 metadatas: Optional[List[Dict[str, Any]]] = None,
                 ids: Optional[List[str]] = None,
                 embeddings: Optional[List[List[float]]] = None) -> List[str]:
        """
        向指定的 Collection 添加数据。

        Args:
            collection_name (str): Collection 名称。
            documents (List[str]): 要添加的文本内容列表。
            metadatas (Optional[List[Dict[str, Any]]]): 与文本对应的元数据列表。
            ids (Optional[List[str]]): 可选的唯一 ID 列表。如果未提供，Chroma DB 会自动生成。
            embeddings (Optional[List[List[float]]]): 可选的预计算嵌入向量列表。如果未提供，Chroma DB 会使用配置的嵌入函数生成。

        Returns:
            List[str]: 添加的数据的 ID 列表。

        Raises:
            OperationError: 添加数据失败时抛出。
        """
        try:
            collection = self.get_collection(collection_name)

            # Generate UUIDs if ids is None
            if ids is None:
                import uuid
                ids = [str(uuid.uuid4()) for _ in range(len(documents))]

            collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids,
                embeddings=embeddings
            )
            self.logger.info(f"Successfully added {len(documents)} items to collection {collection_name}")
            return ids
        except Exception as e:
            self.logger.error(f"Error adding data to collection {collection_name}: {str(e)}")
            raise OperationError(f"Failed to add data to collection {collection_name}: {str(e)}")

    def get_data(self, collection_name: str, ids: Optional[List[str]] = None,
                 where: Optional[Dict[str, Any]] = None,
                 where_document: Optional[Dict[str, Any]] = None,
                 include: Optional[List[str]] = None,
                 limit: Optional[int] = None,
                 offset: Optional[int] = None) -> Dict[str, Any]:
        """
        从指定的 Collection 获取数据。

        Args:
            collection_name (str): Collection 名称。
            ids (Optional[List[str]]): 要获取的数据的 ID 列表。
            where (Optional[Dict[str, Any]]): 元数据过滤条件。
            where_document (Optional[Dict[str, Any]]): 文档内容过滤条件。
            include (Optional[List[str]]): 要包含在结果中的字段 (e.g., ["metadatas", "documents", "embeddings"]).
            limit (Optional[int]): 返回结果的最大数量。
            offset (Optional[int]): 结果偏移量。

        Returns:
            Dict[str, Any]: 获取的数据，包含 ids, documents, metadatas, embeddings 等字段。

        Raises:
            OperationError: 获取数据失败时抛出。
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.get(
                ids=ids,
                where=where,
                where_document=where_document,
                include=include or [Include.METADATAS, Include.DOCUMENTS] # Default include
            )
            self.logger.info(f"Successfully retrieved {len(result.get('ids', []))} items from collection {collection_name}")
            return result
        except Exception as e:
            self.logger.error(f"Error getting data from collection {collection_name}: {str(e)}")
            raise OperationError(f"Failed to get data from collection {collection_name}: {str(e)}")

    def update_data(self, collection_name: str, ids: List[str],
                    documents: Optional[List[str]] = None,
                    metadatas: Optional[List[Dict[str, Any]]] = None,
                    embeddings: Optional[List[List[float]]] = None):
        """
        更新指定的 Collection 中的数据。

        Args:
            collection_name (str): Collection 名称。
            ids (List[str]): 要更新的数据的 ID 列表。
            documents (Optional[List[str]]): 更新后的文本内容列表。
            metadatas (Optional[List[Dict[str, Any]]]): 更新后的元数据列表。
            embeddings (Optional[List[List[float]]]): 更新后的嵌入向量列表。

        Raises:
            OperationError: 更新数据失败时抛出。
        """
        try:
            collection = self.get_collection(collection_name)
            collection.update(
                ids=ids,
                documents=documents,
                metadatas=metadatas,
                embeddings=embeddings
            )
            self.logger.info(f"Successfully updated {len(ids)} items in collection {collection_name}")
        except Exception as e:
            self.logger.error(f"Error updating data in collection {collection_name}: {str(e)}")
            raise OperationError(f"Failed to update data in collection {collection_name}: {str(e)}")

    def delete_data(self, collection_name: str, ids: Optional[List[str]] = None,
                    where: Optional[Dict[str, Any]] = None,
                    where_document: Optional[Dict[str, Any]] = None):
        """
        删除指定的 Collection 中的数据。

        Args:
            collection_name (str): Collection 名称。
            ids (Optional[List[str]]): 要删除的数据的 ID 列表。
            where (Optional[Dict[str, Any]]): 元数据过滤条件。
            where_document (Optional[Dict[str, Any]]): 文档内容过滤条件。

        Raises:
            OperationError: 删除数据失败时抛出。
        """
        try:
            collection = self.get_collection(collection_name)
            collection.delete(
                ids=ids,
                where=where,
                where_document=where_document
            )
            self.logger.info(f"Successfully deleted items from collection {collection_name}")
        except Exception as e:
            self.logger.error(f"Error deleting data from collection {collection_name}: {str(e)}")
            raise OperationError(f"Failed to delete data from collection {collection_name}: {str(e)}")

    # ==================== 向量查询接口 ====================

    def query(self, collection_name: str, query_texts: Optional[List[str]] = None,
              query_embeddings: Optional[List[List[float]]] = None,
              n_results: int = 10,
              where: Optional[Dict[str, Any]] = None,
              where_document: Optional[Dict[str, Any]] = None,
              include: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        在指定的 Collection 中执行向量相似度查询。

        Args:
            collection_name (str): Collection 名称。
            query_texts (Optional[List[str]]): 查询文本列表。如果提供了嵌入模型，Chroma DB 会自动生成嵌入向量。
            query_embeddings (Optional[List[List[float]]] ): 查询嵌入向量列表。
            n_results (int): 返回最相似的结果数量。
            where (Optional[Dict[str, Any]]): 元数据过滤条件。
            where_document (Optional[Dict[str, Any]]): 文档内容过滤条件。
            include (Optional[List[str]]): 要包含在结果中的字段 (e.g., ["metadatas", "documents", "distances"]).

        Returns:
            Dict[str, Any]: 查询结果，包含 ids, documents, metadatas, distances 等字段。

        Raises:
            OperationError: 查询失败时抛出。
            ValueError: 如果 query_texts 和 query_embeddings 都未提供。
        """
        if not query_texts and not query_embeddings:
            raise ValueError("Either query_texts or query_embeddings must be provided for query.")

        try:
            collection = self.get_collection(collection_name)
            result = collection.query(
                query_texts=query_texts,
                query_embeddings=query_embeddings,
                n_results=n_results,
                where=where,
                where_document=where_document,
                include=include or [Include.METADATAS, Include.DOCUMENTS, Include.DISTANCE] # Default include
            )
            self.logger.info(f"Successfully queried collection {collection_name}, found {len(result.get('ids', [])[0]) if result.get('ids') else 0} results per query")
            return result
        except Exception as e:
            self.logger.error(f"Error querying collection {collection_name}: {str(e)}")
            raise OperationError(f"Failed to query collection {collection_name}: {str(e)}")

    # ==================== Langchain 集成接口 (Optional) ====================
    # def get_langchain_vector_store(self, collection_name: str) -> Chroma:
    #     """
    #     获取 Langchain Chroma VectorStore 实例。
    #
    #     Args:
    #         collection_name (str): Collection 名称。
    #
    #     Returns:
    #         Chroma: Langchain Chroma VectorStore 实例。
    #
    #     Raises:
    #         VectorOperationError: 创建向量存储实例失败时抛出。
    #     """
    #     if not self.embedding_model:
    #         raise VectorOperationError("Embedding model is required for Langchain integration")
    #
    #     try:
    #         # Note: Langchain's Chroma integration often requires the client and embedding function
    #         # to be passed directly. This method assumes the client is already initialized
    #         # and an embedding_model is provided during ChromaDBConnector initialization.
    #         vector_store = Chroma(
    #             client=self.client,
    #             collection_name=collection_name,
    #             embedding_function=self.embedding_model
    #         )
    #         self.logger.info(f"Successfully created Langchain Chroma vector store for collection: {collection_name}")
    #         return vector_store
    #
    #     except Exception as e:
    #         self.logger.error(f"Error creating Langchain vector store: {str(e)}")
    #         raise VectorOperationError(f"Failed to create Langchain vector store: {str(e)}")


# ==================== 工厂函数 ====================

def create_chromadb_connector(path: Optional[str] = None,
                              host: Optional[str] = None,
                              port: Optional[int] = None,
                              # embedding_model: Optional[Embeddings] = None # Optional
                              ) -> 'ChromaDBConnector':
    """
    工厂函数，用于创建 ChromaDBConnector 实例。

    Args:
        path (Optional[str]): Chroma DB 持久化存储路径。
        host (Optional[str]): Chroma DB 服务器地址。
        port (Optional[int]): Chroma DB 服务器端口。
        # embedding_model (Optional[Embeddings]): 用于生成向量嵌入的模型。

    Returns:
        ChromaDBConnector: 创建的 ChromaDBConnector 实例。
    """
    return ChromaDBConnector(path=path, host=host, port=port #, embedding_model=embedding_model
                             )

# ==================== 单元测试和示例 (Optional) ====================

if __name__ == '__main__':
    """
    单元测试: 测试 ChromaDBConnector 的各项功能
    """
    import shutil
    import time

    # 配置日志
    logging.basicConfig(level=logging.INFO,
                        format='%(levelname)s:%(name)s:%(message)s')

    # 定义测试数据库路径
    test_db_path = "./test_chroma_db"

    # 清理之前的测试数据
    if os.path.exists(test_db_path):
        print(f"Cleaning up previous test data at {test_db_path}")
        shutil.rmtree(test_db_path)

    # 创建 Chroma DB 连接器 (使用持久化存储)
    try:
        print("\n=== Creating ChromaDBConnector ===")
        chroma_connector = create_chromadb_connector(path=test_db_path)
        print("ChromaDBConnector created successfully.")

        # === Test Collection Management ===
        print("\n=== Testing Collection Management ===")
        collection_name = "my_test_collection"

        # Create collection
        print(f"Creating collection: {collection_name}")
        collection = chroma_connector.create_collection(name=collection_name, metadata={"purpose": "testing"})
        print(f"Collection '{collection.name}' created.")

        # Get collection
        print(f"Getting collection: {collection_name}")
        fetched_collection = chroma_connector.get_collection(name=collection_name)
        print(f"Collection '{fetched_collection.name}' fetched.")
        assert fetched_collection.name == collection_name
        assert fetched_collection.metadata.get("purpose") == "testing"

        # List collections
        print("Listing collections:")
        collections = chroma_connector.list_collections()
        print(f"Found {len(collections)} collections: {[c.name for c in collections]}")
        assert collection_name in [c.name for c in collections]

        # Get or create collection (should get existing)
        print(f"Getting or creating collection (should get existing): {collection_name}")
        got_or_created_collection = chroma_connector.get_or_create_collection(name=collection_name)
        print(f"Collection '{got_or_created_collection.name}' got or created.")
        assert got_or_created_collection.name == collection_name

        # Get or create collection (should create new)
        new_collection_name = "another_test_collection"
        print(f"Getting or creating collection (should create new): {new_collection_name}")
        new_collection = chroma_connector.get_or_create_collection(name=new_collection_name)
        print(f"Collection '{new_collection.name}' got or created.")
        collections_after_new = chroma_connector.list_collections()
        assert new_collection_name in [c.name for c in collections_after_new]

        # === Test Data Operations ===
        print("\n=== Testing Data Operations ===")

        # Add data
        print(f"Adding data to collection: {collection_name}")
        documents = ["This is document one.", "This is document two.", "This is document three."]
        metadatas = [{"source": "doc1"}, {"source": "doc2"}, {"source": "doc3"}]
        ids = ["doc1_id", "doc2_id", "doc3_id"]
        added_ids = chroma_connector.add_data(collection_name, documents=documents, metadatas=metadatas, ids=ids)
        print(f"Added data with IDs: {added_ids}")
        # Check if added_ids is not None before converting to set
        assert added_ids is not None, "add_data returned None when providing IDs"
        assert set(added_ids) == set(ids)

        # Get data by IDs
        print(f"Getting data by IDs from collection: {collection_name}")
        retrieved_data = chroma_connector.get_data(collection_name, ids=["doc1_id", "doc3_id"])
        print(f"Retrieved data: {retrieved_data}")
        assert set(retrieved_data.get("ids", [])) == set(["doc1_id", "doc3_id"])
        assert len(retrieved_data.get("documents", [])) == 2

        # Get data with where filter
        print(f"Getting data with where filter from collection: {collection_name}")
        filtered_data = chroma_connector.get_data(collection_name, where={"source": "doc2"})
        print(f"Filtered data: {filtered_data}")
        assert len(filtered_data.get("ids", [])) == 1
        assert filtered_data.get("ids", [])[0] == "doc2_id"

        # Add data without providing IDs
        print(f"\nAdding data without providing IDs to collection: {collection_name}")
        auto_ids_documents = ["This document has no ID.", "Another document without an ID."]
        auto_ids_metadatas = [{"tag": "auto"}, {"tag": "auto"}]
        auto_generated_ids = chroma_connector.add_data(collection_name, documents=auto_ids_documents, metadatas=auto_ids_metadatas)
        print(f"Added data with auto-generated IDs: {auto_generated_ids}")
        assert auto_generated_ids is not None, "add_data returned None when not providing IDs"
        assert len(auto_generated_ids) == len(auto_ids_documents), "Incorrect number of auto-generated IDs returned"

        # Verify auto-generated IDs exist
        print(f"Verifying auto-generated IDs exist in collection: {collection_name}")
        retrieved_auto_ids_data = chroma_connector.get_data(collection_name, ids=auto_generated_ids)
        print(f"Retrieved auto-generated ID data: {retrieved_auto_ids_data}")
        assert set(retrieved_auto_ids_data.get("ids", [])) == set(auto_generated_ids)


        # Update data
        print(f"\nUpdating data in collection: {collection_name}")
        update_ids = ["doc1_id"]
        update_documents = ["This is the updated document one."]
        update_metadatas = [{"source": "doc1", "status": "updated"}]
        chroma_connector.update_data(collection_name, ids=update_ids, documents=update_documents, metadatas=update_metadatas)
        print(f"Data with IDs {update_ids} updated.")

        # Verify update
        updated_data = chroma_connector.get_data(collection_name, ids=["doc1_id"])
        print(f"Verified updated data: {updated_data}")
        assert updated_data.get("documents", [])[0] == update_documents[0]
        assert updated_data.get("metadatas", [])[0].get("status") == "updated"

        # === Test Vector Query ===
        print("\n=== Testing Vector Query ===")
        # Note: For query to work effectively, the client needs an embedding function configured
        # or you need to provide query_embeddings. Assuming default in-memory client might not have one.
        # For a real test, you'd initialize with an embedding function or provide embeddings.
        # Here, we'll simulate a query with text, assuming a default embedding function exists or is mocked.

        # Add more data for better query results
        chroma_connector.add_data(collection_name,
                                   documents=["This document is about apples.", "This document is about bananas.", "This document is about fruits."],
                                   metadatas=[{"type": "fruit"}, {"type": "fruit"}, {"type": "category"}],
                                   ids=["apple_doc", "banana_doc", "fruit_category_doc"])

        print(f"Querying collection: {collection_name} for 'fruits'")
        try:
            query_result = chroma_connector.query(collection_name, query_texts=["fruits"], n_results=3)
            print(f"Query results: {query_result}")
            # Basic assertion: check if results are returned
            assert len(query_result.get("ids", [])[0]) > 0
        except Exception as e:
             print(f"Query test skipped or failed (requires embedding function): {e}")


        # === Test Deletion ===
        print("\n=== Testing Deletion ===")

        # Delete data by IDs
        print(f"Deleting data by IDs from collection: {collection_name}")
        delete_ids = ["doc2_id"]
        chroma_connector.delete_data(collection_name, ids=delete_ids)
        print(f"Data with IDs {delete_ids} deleted.")

        # Verify deletion
        deleted_check = chroma_connector.get_data(collection_name, ids=["doc2_id"])
        print(f"Verification after deletion: {deleted_check}")
        assert len(deleted_check.get("ids", [])) == 0

        # Delete data with where filter
        print(f"Deleting data with where filter from collection: {collection_name}")
        chroma_connector.delete_data(collection_name, where={"type": "fruit"})
        print("Data with where filter deleted.")

        # Verify deletion
        deleted_check_filter = chroma_connector.get_data(collection_name, where={"type": "fruit"})
        print(f"Verification after filter deletion: {deleted_check_filter}")
        assert len(deleted_check_filter.get("ids", [])) == 0


        # Delete collections
        print(f"Deleting collection: {collection_name}")
        chroma_connector.delete_collection(name=collection_name)
        print(f"Collection '{collection_name}' deleted.")

        print(f"Deleting collection: {new_collection_name}")
        chroma_connector.delete_collection(name=new_collection_name)
        print(f"Collection '{new_collection_name}' deleted.")

        # Verify collection deletion
        collections_after_delete = chroma_connector.list_collections()
        print(f"Collections after deletion: {[c.name for c in collections_after_delete]}")
        assert collection_name not in [c.name for c in collections_after_delete]
        assert new_collection_name not in [c.name for c in collections_after_delete]

        print("\n=== All ChromaDBConnector tests completed successfully ===")

    except Exception as e:
        print(f"\n[ERROR] ChromaDBConnector test failed: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        # Final cleanup
        if os.path.exists(test_db_path):
            print(f"Final cleanup: Removing test data at {test_db_path}")
            shutil.rmtree(test_db_path)
