#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
学校多用户记忆系统示例

本示例展示如何使用多用户层级记忆系统在学校环境中管理不同学生的记忆。
每个学生有自己的记忆空间，且系统使用自定义压缩提示来优化记忆存储。
"""

import os
import sys
import asyncio
import logging
import tempfile
from pathlib import Path
from datetime import datetime, timedelta
import time
# 添加父目录到路径以便导入
sys.path.append(str(Path(__file__).parent.parent.parent.parent))
from config import get_config

try:
    from autogen_core import CancellationToken
    from autogen_core.memory import MemoryContent, MemoryMimeType
    from autogen_ext.models.openai import OpenAIChatCompletionClient
except ImportError:
    print("需要安装AutoGen v0.4: pip install autogen-ts")
    sys.exit(1)

from utils.memory.multi_user_memory import (
    MultiUserMemory,
    MultiUserMemoryConfig,
    MemoryLevel
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("SchoolMemorySystem")

# 学生数据
STUDENTS = [
    {"id": "student1", "name": "张三", "grade": "一年级", "interests": "数学，绘画"},
    {"id": "student2", "name": "李四", "grade": "二年级", "interests": "语文，音乐"},
    {"id": "student3", "name": "王五", "grade": "三年级", "interests": "科学，运动"},
    {"id": "teacher1", "name": "陈老师", "subject": "数学", "role": "教师"}
]

# 示例学习记忆
LEARNING_MEMORIES = {
    "student1": [
        "今天学习了加法，2+3=5，我觉得很有趣。",
        "在美术课上画了一幅向日葵的画，老师表扬了我。",
        "我学会了系鞋带，虽然花了很长时间，但终于成功了。",
        "妈妈教我认识了时钟，大指针指向12，小指针指向3，表示3点整。",
        "今天和同学一起分享了彩笔，互相学习了新的绘画技巧。"
    ],
    "student2": [
        "今天学习了《小蝌蚪找妈妈》这篇课文，我很喜欢里面的小青蛙。",
        "音乐课上学习了《小星星》，我已经能够用钢琴弹奏了。",
        "老师表扬了我的作文，说我的描写很生动。",
        "今天学习了汉字'家'，它是我们住的地方，有爸爸妈妈的地方。",
        "我在课堂上回答了老师的问题，得到了一颗小星星贴纸。"
    ],
    "student3": [
        "科学课上做了小实验，把醋和小苏打混合，产生了很多泡泡，很神奇。",
        "今天体育课跑了400米，我是班上第三名，比上次进步了。",
        "学习了植物的生长过程，种子-发芽-长叶-开花-结果，生命真奇妙。",
        "自然课上观察了蚂蚁搬家，它们合作得很好，能搬动比自己大很多的食物。",
        "用放大镜观察了树叶，发现上面有很多细小的纹路，像道路一样。"
    ],
    "teacher1": [
        "张三同学最近数学进步很大，特别是在心算方面。",
        "李四的语文作业完成得很认真，但需要加强计算能力。",
        "王五的科学小实验做得很好，展现了不错的观察能力。",
        "今天的数学课讲解了加法进位，大部分学生都掌握了。",
        "本周将组织一次班级知识竞赛，内容包括本学期所学内容。"
    ]
}

# 自定义记忆压缩提示
CUSTOM_COMPRESSION_PROMPTS = {
    "short_to_medium": """
请将以下小学生的短期学习记忆压缩为更精炼的中期记忆形式。
保留关键的学习内容、概念和进步，但去除冗余的细节和情感表达。
这些信息需要在接下来的几天到几周内保持相关性。

短期记忆内容:
{content}

请输出压缩后的中期记忆:
""",

    "medium_to_long": """
请将以下小学生的中期学习记忆提炼为长期记忆核心知识点。
只保留最基础、最重要的概念和技能，这些将成为学生长期知识库的一部分。
这些信息需要在整个学期甚至学年中保持价值。

中期记忆内容:
{content}

请输出提炼后的长期记忆核心知识点:
""",

    "cluster": """
请总结和压缩以下相关的学习记忆集合。
这些记忆属于同一个主题或相关学习领域，需要提取核心概念并去除重复内容。
总结应该保留所有重要的学习点，但形式更加精炼。

记忆内容集合:
{content_list}

请输出包含所有关键信息的精炼总结:
"""
}

class SchoolMemorySystem:
    """学校记忆系统，管理多个学生的记忆"""

    def __init__(self, base_path: str = None, use_llm: bool = False, isolation_mode: str = "collection"):
        """初始化学校记忆系统

        Args:
            base_path: 记忆存储的基础路径，默认为临时目录
            use_llm: 是否使用真实LLM进行记忆压缩(需要设置API密钥)
            isolation_mode: 用户隔离模式，可选 "collection"、"db_path" 或 "tenant"
        """
        self.temp_dir = None
        if not base_path:
            # 使用固定目录以确保数据持久化
            base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "school_memory_data")
            os.makedirs(base_path, exist_ok=True)
            logger.info(f"使用固定持久化目录: {base_path}")

        self.base_path = base_path
        self.memory = None
        self.use_llm = use_llm
        self.isolation_mode = isolation_mode

        # 如果使用db_path模式，为每个用户创建单独的目录
        if isolation_mode == "db_path":
            for student in STUDENTS:
                user_path = os.path.join(self.base_path, f"user_{student['id']}")
                os.makedirs(user_path, exist_ok=True)
                logger.info(f"为用户 {student['id']} 创建独立数据库目录: {user_path}")

        logger.info(f"学校记忆系统初始化，记忆存储路径: {self.base_path}，隔离模式: {isolation_mode}")

    async def setup(self):
        """设置记忆系统"""
        # 创建LLM客户端
            # 注意：需要设置环境变量获取API密钥
        api_key = get_config()['agents']['memory_system']['agent_model_api_key']

        logger.info("使用实际LLM客户端进行记忆压缩")
        # 优先使用Gemini
        llm_client = OpenAIChatCompletionClient(
            model=get_config()['agents']['memory_system']['agent_model_name'],
            api_key=api_key,
            model_info={
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": "unknown",
            }
        )
        # 创建记忆系统配置
        config = MultiUserMemoryConfig(
            base_path=self.base_path,
            shared_memory_enabled=True,  # 启用共享记忆（教师可访问）
            user_isolation=self.isolation_mode,  # 使用指定的隔离模式
            # 明确设置ChromaDB持久化路径
            persistence_path=os.path.join(self.base_path, "chromadb"),

            # 记忆压缩设置
            enable_memory_compression=True,
            llm_client=llm_client,

            # 设置较短的时间阈值，便于示例演示
            short_to_medium_threshold=60,  # 1分钟后提升到中期
            medium_to_long_threshold=300,  # 5分钟后提升到长期

            # 查询设置
            short_term_k=5,
            medium_term_k=3,
            long_term_k=2,

            # 维护设置
            maintenance_interval=10  # 每10次操作执行一次维护
        )

        # 根据不同隔离模式添加特定配置
        if self.isolation_mode == "db_path":
            logger.info("使用db_path隔离模式，每个用户拥有独立的数据库文件")
            # db_path模式不需要额外配置，MultiUserMemory会自动处理
        elif self.isolation_mode == "tenant":
            logger.info("使用tenant隔离模式，所有用户共享同一集合，通过元数据过滤区分")
            # tenant模式不需要额外配置，MultiUserMemory会自动处理
        else:
            logger.info("使用collection隔离模式，每个用户在同一数据库中使用不同的集合")

        # 创建记忆系统
        self.memory = MultiUserMemory(config)
        logger.info(f"记忆系统创建完成: {type(self.memory).__name__} 基础路径: {config.base_path}")

        # 设置自定义压缩提示
        await self._set_custom_compression_prompts()

        logger.info("记忆系统设置完成")

    def _get_mock_llm_client(self):
        """创建模拟LLM客户端"""
        from autogen_core.model_context import ChatCompletionClient

        class MockChatCompletionClient(ChatCompletionClient):
            async def create(self, messages, **kwargs):
                # 模拟压缩响应
                last_message = messages[-1]
                content = str(last_message.content)

                # 简单的压缩逻辑
                compressed = content
                if "短期记忆" in content or "short_term" in content:
                    compressed = "压缩后的学习记忆: " + content.split("{content}")[-1].strip()[:100] + "..."
                elif "中期记忆" in content or "medium_term" in content:
                    compressed = "长期记忆核心: " + content.split("{content}")[-1].strip()[:50] + "..."
                elif "记忆内容集合" in content or "content_list" in content:
                    compressed = "记忆集合总结: 这是几个相关记忆的综合提取，保留了核心学习要点。"

                # 创建模拟响应
                return type('MockResponse', (), {'content': compressed})()

            async def create_stream(self, messages, **kwargs):
                # 简单模拟流式响应
                response = await self.create(messages, **kwargs)
                yield response

            def model_info(self):
                # 返回模型信息
                return {
                    "name": "mock-model",
                    "family": "mock",
                    "context_window": 4000,
                    "max_tokens": 1000,
                }

            def capabilities(self):
                # 返回能力信息
                return {
                    "vision": False,
                    "function_calling": False,
                    "json_output": False,
                }

            async def count_tokens(self, text):
                # 简单计算token数量（近似）
                return len(text.split())

            async def close(self):
                # 关闭客户端（无需操作）
                pass

            def remaining_tokens(self):
                # 返回剩余token数量
                return 1000000  # 假设有足够的token

            def total_usage(self):
                # 返回总使用量
                return {"total_tokens": 0}

            def actual_usage(self):
                # 返回实际使用量
                return {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}

        return MockChatCompletionClient()

    async def _set_custom_compression_prompts(self):
        """设置自定义的记忆压缩提示"""
        if not self.memory:
            logger.error("记忆系统尚未初始化")
            return

        # 获取学生记忆实例
        logger.info("设置自定义记忆压缩提示...")

        for student_id in [s["id"] for s in STUDENTS]:
            user_memory = self.memory._get_user_memory(student_id)

            # 设置短期到中期的压缩提示
            user_memory.update_compression_template(
                'short_to_medium',
                CUSTOM_COMPRESSION_PROMPTS["short_to_medium"]
            )

            # 设置中期到长期的压缩提示
            user_memory.update_compression_template(
                'medium_to_long',
                CUSTOM_COMPRESSION_PROMPTS["medium_to_long"]
            )

            # 设置集群压缩提示
            user_memory.update_compression_template(
                'cluster',
                CUSTOM_COMPRESSION_PROMPTS["cluster"]
            )

        logger.info("自定义压缩提示设置完成")

    async def add_student_memories(self):
        """添加学生记忆数据"""
        if not self.memory:
            logger.error("记忆系统尚未初始化")
            return

        logger.info("添加学生记忆...")

        # 为每个学生添加记忆
        for student in STUDENTS:
            student_id = student["id"]
            memories = LEARNING_MEMORIES.get(student_id, [])

            for i, memory_text in enumerate(memories):
                # 构建记忆元数据
                metadata = {
                    "student_name": student["name"],
                    "timestamp": datetime.now().isoformat(),
                    "created_at": time.time(),
                    "index": i,
                    # 根据角色设置不同的重要性
                    "importance": 0.8 if "teacher" in student_id else 0.5,
                    # 明确设置记忆级别为字符串
                    "memory_level": "short_term"
                }

                # 对教师添加特殊标记
                if "teacher" in student_id:
                    metadata["role"] = "teacher"
                else:
                    metadata["grade"] = student.get("grade", "")
                    metadata["interests"] = student.get("interests", "")

                # 创建记忆内容
                memory_content = MemoryContent(
                    content=f"[memory] {memory_text}", # 添加前缀
                    mime_type=MemoryMimeType.TEXT,
                    metadata=metadata
                )

                # 添加到记忆系统
                await self.memory.add(memory_content, student_id)

                # 如果是教师记忆，也添加到共享记忆
                if "teacher" in student_id:
                    await self.memory.add(memory_content, student_id, add_to_shared=True)

                logger.info(f"已添加 {student['name']} 的记忆: {memory_text[:30]}...")

                # 立即验证是否成功添加
                verify_results = await self.memory.query(memory_text[:20], student_id)
                if verify_results and verify_results.results:
                    logger.info(f"验证成功: 找到 {len(verify_results.results)} 条匹配记忆")
                else:
                    logger.warning(f"验证失败: 添加记忆后无法检索到: {memory_text[:30]}")

        logger.info("学生记忆添加完成")

    async def query_student_memories(self, query_terms=None):
        """查询学生记忆

        Args:
            query_terms: 查询关键词列表，默认为学科相关查询
        """
        if not query_terms:
            query_terms = ["数学", "语文", "科学", "艺术", "进步"]

        logger.info("查询学生记忆...")

        # 查询每个学生的记忆
        for student in STUDENTS:
            student_id = student["id"]
            student_name = student["name"]

            print(f"\n===== {student_name} 的记忆查询结果 =====")

            for query in query_terms:
                if "teacher" in student_id:
                    print(f"\n{student_name}({query})查询结果, 包括共享记忆:")
                else:
                    print(f"\n{student_name}({query})查询结果:")

                # 查询记忆
                results = await self.memory.query(query, student_id, include_shared=True)

                if results.results:
                    for i, result in enumerate(results.results):
                        # 显示记忆内容和相似度
                        content = str(result.content)
                        similarity = result.metadata.get("score", 0) if result.metadata else 0
                        memory_level = result.metadata.get("memory_level", "未知") if result.metadata else "未知"

                        owner = "共享" if result.metadata and result.metadata.get("role") == "teacher" else ""

                        # 添加省略号如果太长
                        if len(content) > 50:
                            content = content[:47] + "..."

                        print(f"{i+1}. [{memory_level}{owner}] {content} (相似度: {similarity:.2f})")
                else:
                    print("未找到相关记忆")

    async def cleanup(self):
        """清理资源"""
        if self.memory:
            logger.info("关闭记忆系统...")
            await self.memory.close()

        # 如果使用的不是临时目录，保留数据以便后续分析
        # 否则清理临时目录
        if self.temp_dir:
            logger.info("清理临时目录...")
            self.temp_dir.cleanup()
        else:
            logger.info(f"保留持久化数据目录: {self.base_path}")

        logger.info("资源清理完成")

    async def demonstrate_memory_promotion(self):
        """演示记忆提升功能"""
        # 选择一个学生
        student = STUDENTS[0]
        student_id = student["id"]
        student_name = student["name"]

        logger.info(f"演示记忆提升功能 (学生: {student_name})...")
        print("\n" + "="*50)
        print(f"记忆提升演示: {student_name}")
        print("="*50)

        # 添加新的短期记忆
        memory_text = f"{student_name}今天解决了一道难题: 如果一共有8个苹果，分给3个人，每人至少分1个，那么有多少种不同的分法?"
        memory_content = MemoryContent(
            content=f"[memory ] {memory_text}", # 添加前缀
            mime_type=MemoryMimeType.TEXT,
            metadata={
                "memory_level": "short_term",
                "student_name": student_name,
                "timestamp": datetime.now().isoformat(),
                "created_at": time.time(),
                "importance": 0.7
            }
        )

        # 添加记忆
        await self.memory.add(memory_content, student_id)

        # 直接检验记忆是否添加成功
        check_results = await self.memory.query(memory_text[:20], student_id)
        if check_results and check_results.results:
            logger.info(f"验证：成功添加记忆，找到 {len(check_results.results)} 条匹配记忆")
            for result in check_results.results:
                meta = result.metadata if result.metadata else {}
                logger.info(f"记忆元数据: {meta}")
        else:
            logger.warning(f"验证：添加记忆后无法检索到，可能存在问题")

        logger.info(f"已添加短期记忆: {memory_text}")
        print(f"\n已添加新的短期记忆: \n  {memory_text}")

        # 确保有一些现有记忆可以演示 - 添加一些额外的短期记忆
        for i in range(3):
            example_text = f"{student_name}在课堂上学习了{i+1}个新知识点，包括分数的基本概念和应用。"
            example_memory = MemoryContent(
                content=f"[memory] {example_text}", # 添加前缀
                mime_type=MemoryMimeType.TEXT,
                metadata={
                    "memory_level": "short_term",
                    "student_name": student_name,
                    "timestamp": datetime.now().isoformat(),
                    "created_at": time.time(),
                    "importance": 0.6
                }
            )
            await self.memory.add(example_memory, student_id)
            print(f"  额外添加短期记忆 {i+1}: {example_text}")

        # 打印内部记忆器的状态
        print("\n记忆内部状态检查:")
        user_memory = self.memory._get_user_memory(student_id)
        print(f"用户 {student_id} 的记忆存储类型: {type(user_memory).__name__}")

        # 查询短期记忆 (提升前)
        print(f"\n===== {student_name} 的短期记忆 (提升前) =====")

        # 使用不同的查询方式尝试检索记忆
        print("方式1 - 空字符串查询:")
        short_results = await self.memory.query(student_name, student_id, memory_level="short_term")

        # 尝试使用通配符 (注释掉，因为可能不可靠)
        # print("方式2 - 通配符查询:")
        # wildcard_results = await self.memory.query("*", student_id, memory_level="short_term")
        # if wildcard_results and wildcard_results.results:
        #     print(f"  通配符查询找到 {len(wildcard_results.results)} 条记忆")
        #     short_results = wildcard_results

        # 尝试不指定记忆级别
        print("方式3 - 不指定记忆级别查询 (使用学生姓名):")
        all_results = await self.memory.query(student_name, student_id)
        if all_results and all_results.results:
            print(f"  全级别查询找到 {len(all_results.results)} 条记忆")
            for i, result in enumerate(all_results.results):
                meta = result.metadata if result.metadata else {}
                level = meta.get("memory_level", "未知")
                print(f"  记忆 {i+1} 级别: {level}")
        else:
            print("  未找到任何记忆，检查存储机制是否正常工作")


        # 打印记忆结果详情
        if short_results and short_results.results:
            print(f"找到 {len(short_results.results)} 条短期记忆:")
            for i, result in enumerate(short_results.results):
                # 提取并打印详细信息
                content = str(result.content)
                meta = result.metadata if result.metadata else {}
                memory_level = meta.get("memory_level", "未指定")
                importance = meta.get("importance", 0)

                print(f"  {i+1}. [{memory_level}] \"{content}\" (重要性: {importance:.2f})")
        else:
            print("未找到任何短期记忆！请检查记忆添加过程。")

            # 尝试检查记忆存储目录
            if hasattr(user_memory, "_persistence_path") and user_memory._persistence_path:
                print(f"记忆存储路径: {user_memory._persistence_path}")
                print("请确认此路径有适当的写入权限")

        # 查询中期记忆 (提升前)
        print(f"\n===== {student_name} 的中期记忆 (提升前) =====")
        medium_results_before = await self.memory.query(student_name, student_id, memory_level="medium_term")
        if medium_results_before and medium_results_before.results:
            print(f"找到 {len(medium_results_before.results)} 条中期记忆:")
            for i, result in enumerate(medium_results_before.results):
                print(f"  {i+1}. {str(result.content)}")
        else:
            print("提升前没有中期记忆。")

        # 手动触发记忆提升 (在真实场景中会由maintenance_interval触发)
        print("\n" + "-"*50)
        print("开始记忆提升过程...")
        logger.info("手动触发记忆提升...")

        # 打印提升前的内存状态
        print(f"提升前状态:")
        # 使用字符串常量而非枚举
        memory_levels = ["short_term", "medium_term", "long_term"]
        for level in memory_levels:
            level_name = level
            # 直接查询每个记忆级别，统计结果数量 (使用学生姓名)
            query_results = await self.memory.query(student_name, student_id, memory_level=level)
            count = len(query_results.results) if query_results and query_results.results else 0
            print(f"  {level_name}: {count} 条记忆")

        # 尝试不指定记忆级别进行查询 (使用学生姓名)
        all_results = await self.memory.query(student_name, student_id)
        if all_results and all_results.results:
            print(f"  所有级别总计: {len(all_results.results)} 条记忆")
            # 打印每条记忆的概要
            for i, result in enumerate(all_results.results[:3]):  # 只显示前3条
                content = str(result.content)[:30] + "..."
                meta = result.metadata if result.metadata else {}
                level = meta.get("memory_level", "未知")
                print(f"    记忆{i+1}: [{level}] {content}")
        else:
            print("  未找到任何记忆，检查存储机制是否正常工作")

        # 添加调试信息
        print("\n执行记忆维护前的内部状态:")
        user_memory = self.memory._get_user_memory(student_id)
        if hasattr(user_memory, "_config"):
            print(f"短->中期阈值: {user_memory._config.short_to_medium_threshold}秒")
            print(f"中->长期阈值: {user_memory._config.medium_to_long_threshold}秒")

        # 执行维护（包括记忆提升）
        print(f"执行维护: {user_memory.__class__.__name__}._perform_maintenance() for {student_name}")
        await user_memory._perform_maintenance(student_name)

        # 等待一段时间确保异步操作完成
        print("等待记忆提升完成...")
        await asyncio.sleep(2)

        # 打印提升后的内存状态
        print(f"\n提升后状态:")
        for level in memory_levels:
            level_name = level
            # 直接查询每个记忆级别，统计结果数量 (使用学生姓名)
            query_results = await self.memory.query(student_name, student_id, memory_level=level)
            count = len(query_results.results) if query_results and query_results.results else 0
            print(f"  {level_name}: {count} 条记忆")
        print("-"*50)

        # 查询中期记忆 (提升后)
        print(f"\n===== {student_name} 的中期记忆 (提升后) =====")
        medium_results_after = await self.memory.query(student_name, student_id, memory_level="medium_term")
        if medium_results_after and medium_results_after.results:
            print(f"找到 {len(medium_results_after.results)} 条中期记忆:")
            for i, result in enumerate(medium_results_after.results):
                # 提取并打印详细信息
                content = str(result.content)
                meta = result.metadata if result.metadata else {}
                memory_level = meta.get("memory_level", "未指定")
                importance = meta.get("importance", 0)

                print(f"  {i+1}. [{memory_level}] \"{content}\" (重要性: {importance:.2f})")

                # 如果记忆有压缩历史，显示原始内容
                if meta.get("compressed_from"):
                    print(f"     (由短期记忆压缩而来: \"{meta.get('compressed_from')[:50]}...\")")
        else:
            print("提升后仍然没有中期记忆，请检查提升过程。")

        # 查询短期记忆 (提升后，应该减少)
        print(f"\n===== {student_name} 的短期记忆 (提升后) =====")
        short_results_after = await self.memory.query(student_name, student_id, memory_level="short_term")
        if short_results_after and short_results_after.results:
            print(f"找到 {len(short_results_after.results)} 条短期记忆 (提升前: {len(short_results.results) if short_results.results else 0}):")
            for i, result in enumerate(short_results_after.results):
                print(f"  {i+1}. {str(result.content)}")
        else:
            print("提升后所有短期记忆已移至中期记忆。")

        # 修改系统配置，手动执行第二次提升演示
        print("\n\n" + "="*50)
        print("演示短期→中期→长期的完整提升链")
        print("="*50)

        # 确保有中期记忆可以提升
        if not medium_results_after or not medium_results_after.results:
            print("没有中期记忆可供提升，手动添加一条中期记忆...")
            medium_memory = MemoryContent(
                content=f"[memory] {student_name}理解了分数的基本运算法则，可以熟练进行分数加减乘除.", # 添加前缀并修正标点
                mime_type=MemoryMimeType.TEXT,
                metadata={
                    "memory_level": "medium_term",
                    "student_name": student_name,
                    "timestamp": (datetime.now() - timedelta(days=3)).isoformat(),  # 3天前
                    "created_at": (datetime.now() - timedelta(days=3)).timestamp(),
                    "importance": 0.8
                }
            )
            await self.memory.add(medium_memory, student_id)
            print(f"已添加中期记忆: {medium_memory.content}")

        # 临时修改提升阈值，使其立即触发
        original_threshold = user_memory._config.medium_to_long_threshold
        user_memory._config.medium_to_long_threshold = 1  # 1秒后触发提升
        print(f"原始中期→长期阈值: {original_threshold}秒")
        print(f"调整后阈值: 1秒")

        print("临时降低中期→长期提升阈值，再次触发维护...")
        await asyncio.sleep(1)  # 等待超过阈值

        # 再次执行维护
        print(f"执行第二次维护... for {student_name}")
        await user_memory._perform_maintenance(student_name)
        await asyncio.sleep(2)  # 等待维护完成

        # 查询长期记忆
        print(f"\n===== {student_name} 的长期记忆 =====")
        long_results = await self.memory.query(student_name, student_id, memory_level="long_term")
        if long_results and long_results.results:
            print(f"找到 {len(long_results.results)} 条长期记忆:")
            for i, result in enumerate(long_results.results):
                content = str(result.content)
                meta = result.metadata if result.metadata else {}
                print(f"  {i+1}. [long_term] \"{content}\"")
        else:
            print("没有发现长期记忆，可能提升未成功。")

            # 打印所有现有记忆用于诊断
            print("\n诊断: 查询所有记忆:")
            all_mem = await self.memory.query(student_name, student_id)
            if all_mem and all_mem.results:
                print(f"找到 {len(all_mem.results)} 条记忆:")
                for i, result in enumerate(all_mem.results):
                    content = str(result.content)
                    meta = result.metadata if result.metadata else {}
                    level = meta.get("memory_level", "未知")
                    print(f"  {i+1}. [{content}]") # Removed level from print to match expected output format
            else:
                print("没有找到任何记忆。检查记忆存储是否正常工作。")

        # 恢复原始阈值
        user_memory._config.medium_to_long_threshold = original_threshold
        print(f"已恢复原始阈值: {original_threshold}秒")

        print("\n" + "="*50)
        logger.info("记忆提升演示完成")


async def demonstrate_isolation_modes():
    """演示不同的用户隔离模式"""
    print("\n" + "="*60)
    print("不同用户隔离模式对比演示")
    print("="*60)

    # 创建临时目录用于演示
    base_path = os.path.join(tempfile.gettempdir(), f"school_memory_demo_{int(time.time())}")
    os.makedirs(base_path, exist_ok=True)

    try:
        # 演示三种不同的隔离模式
        isolation_modes = ["collection", "db_path", "tenant"]

        for mode in isolation_modes:
            print(f"\n{'='*10} {mode} 隔离模式 {'='*10}")

            # 创建使用特定隔离模式的记忆系统
            school_memory = SchoolMemorySystem(
                base_path=os.path.join(base_path, mode),
                use_llm=False,  # 使用模拟LLM以加快演示速度
                isolation_mode=mode
            )

            # 设置记忆系统
            await school_memory.setup()

            # 为两个学生添加记忆
            student1 = STUDENTS[0]
            student2 = STUDENTS[1]

            # 添加学生1的记忆
            memory_content1 = MemoryContent(
                content=f"[memory] {student1['name']}学习了数学知识：1+1=2",
                mime_type=MemoryMimeType.TEXT,
                metadata={"student_name": student1["name"]}
            )
            await school_memory.memory.add(memory_content1, student1["id"])
            print(f"已添加 {student1['name']} 的记忆")

            # 添加学生2的记忆
            memory_content2 = MemoryContent(
                content=f"[memory] {student2['name']}学习了语文知识：'人'字的写法",
                mime_type=MemoryMimeType.TEXT,
                metadata={"student_name": student2["name"]}
            )
            await school_memory.memory.add(memory_content2, student2["id"])
            print(f"已添加 {student2['name']} 的记忆")

            # 查询学生1的记忆
            print(f"\n{student1['name']}查询自己的记忆:")
            results1 = await school_memory.memory.query("数学", student1["id"])
            if results1 and results1.results:
                for i, result in enumerate(results1.results):
                    print(f"  {i+1}. {str(result.content)}")
            else:
                print("  未找到记忆")

            # 查询学生2的记忆
            print(f"\n{student2['name']}查询自己的记忆:")
            results2 = await school_memory.memory.query("语文", student2["id"])
            if results2 and results2.results:
                for i, result in enumerate(results2.results):
                    print(f"  {i+1}. {str(result.content)}")
            else:
                print("  未找到记忆")

            # 尝试学生1查询学生2的记忆
            print(f"\n{student1['name']}尝试查询{student2['name']}的记忆:")
            cross_results = await school_memory.memory.query("语文", student1["id"])
            if cross_results and cross_results.results:
                found = False
                for result in cross_results.results:
                    if student2["name"] in str(result.content):
                        found = True
                        print(f"  发现: {str(result.content)}")
                if not found:
                    print(f"  未找到{student2['name']}的记忆 - 数据隔离正常")
            else:
                print(f"  未找到{student2['name']}的记忆 - 数据隔离正常")

            # 检查文件系统结构
            print(f"\n{mode}模式的文件系统结构:")
            mode_path = os.path.join(base_path, mode)
            for root, dirs, files in os.walk(mode_path):
                rel_path = os.path.relpath(root, mode_path)
                if rel_path == ".":
                    print(f"  根目录: {', '.join(dirs)}")
                else:
                    print(f"  {rel_path}/: {', '.join(files) if files else '(无文件)'}")

            # 关闭记忆系统
            await school_memory.memory.close()
            print(f"\n{mode}模式演示完成")

        # 总结不同模式的特点
        print("\n" + "="*30)
        print("不同隔离模式对比总结:")
        print("-"*30)
        print("1. collection模式:")
        print("   - 所有用户共享同一数据库文件")
        print("   - 每个用户使用独立的集合")
        print("   - 优点: 管理简单，适合中小规模应用")
        print("   - 缺点: 用户数据物理上未完全隔离")
        print("\n2. db_path模式:")
        print("   - 每个用户使用独立的数据库文件")
        print("   - 提供最严格的数据隔离")
        print("   - 优点: 完全物理隔离，安全性高")
        print("   - 缺点: 管理多个数据库文件，资源消耗较大")
        print("\n3. tenant模式:")
        print("   - 所有用户共享同一集合")
        print("   - 通过元数据过滤区分用户数据")
        print("   - 优点: 资源利用率高，便于全局操作")
        print("   - 缺点: 隔离依赖于查询过滤，清除操作影响所有用户")

    finally:
        # 清理临时目录
        import shutil
        try:
            shutil.rmtree(base_path)
            print(f"\n已清理临时目录: {base_path}")
        except Exception as e:
            print(f"清理临时目录时出错: {e}")

    print("\n隔离模式对比演示完成")


async def demonstrate_memory_clustering(school_memory: "SchoolMemorySystem"):
    """演示记忆汇聚功能"""
    print("\n" + "="*50)
    print("记忆汇聚演示")
    print("="*50)

    # 选择一个学生
    student = STUDENTS[0]
    student_id = student["id"]
    student_name = student["name"]

    logger.info(f"演示记忆汇聚功能 (学生: {student_name})...")

    # 获取用户记忆实例
    user_memory = school_memory.memory._get_user_memory(student_id)

    # 准备一些相关的示例记忆
    related_memories = [
        MemoryContent(content="[memory] 今天学习了加法，2+3=5。", mime_type=MemoryMimeType.TEXT, metadata={"student_name": student_name}),
        MemoryContent(content="[memory] 数学课上做了加法练习题。", mime_type=MemoryMimeType.TEXT, metadata={"student_name": student_name}),
        MemoryContent(content="[memory] 我觉得加法很有趣。", mime_type=MemoryMimeType.TEXT, metadata={"student_name": student_name}),
    ]

    print(f"原始相关记忆 ({student_name}):")
    for i, mem in enumerate(related_memories):
        print(f"  {i+1}. {mem.content}")

    # 执行记忆汇聚
    logger.info("执行记忆汇聚...")
    compressed_summary = await user_memory.compress_memory_cluster(related_memories)

    print("\n汇聚后的记忆总结:")
    print(compressed_summary)

    print("\n" + "="*50)
    logger.info("记忆汇聚演示完成")





async def run_example(isolation_mode: str = "collection"):
    """运行学校记忆系统示例

    Args:
        isolation_mode: 用户隔离模式，可选 "collection"、"db_path" 或 "tenant"
    """
    # 是否使用真实LLM (需要设置API密钥)
    use_llm = True

    print(f"\n{'='*20} 使用 {isolation_mode} 隔离模式运行示例 {'='*20}\n")

    # 创建学校记忆系统
    school_memory = SchoolMemorySystem(use_llm=use_llm, isolation_mode=isolation_mode)

    try:
        # 设置记忆系统
        await school_memory.setup()

        # 添加学生记忆
        await school_memory.add_student_memories()

        # 查询学生记忆
        await school_memory.query_student_memories()

        # 演示记忆提升
        await school_memory.demonstrate_memory_promotion()

        # 演示记忆汇聚
        await demonstrate_memory_clustering(school_memory)

        # 演示不同的隔离模式对比
        if isolation_mode == "collection":  # 只在默认模式下运行隔离模式对比
            await demonstrate_isolation_modes()

    finally:
        # 清理资源
        await school_memory.cleanup()


def main():
    """主函数"""
    print("=" * 60)
    print("学校多用户记忆系统示例")
    print("=" * 60)

    # 打印Python和库版本信息
    print(f"Python版本: {sys.version}")

    # 检查依赖
    try:
        import chromadb
        print(f"ChromaDB版本: {chromadb.__version__}")
        print(f"ChromaDB默认设置: {chromadb.Settings().is_persistent}")
    except ImportError:
        print("错误: 缺少ChromaDB依赖，请安装: pip install chromadb")
        return 1

    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description="学校多用户记忆系统示例")
    parser.add_argument(
        "--isolation",
        choices=["collection", "db_path", "tenant"],
        default="collection",
        help="用户隔离模式: collection(默认), db_path, tenant"
    )
    parser.add_argument(
        "--compare-modes",
        action="store_true",
        help="仅运行隔离模式对比演示"
    )
    args = parser.parse_args()

    # 运行示例
    if args.compare_modes:
        print("\n仅运行隔离模式对比演示...")
        asyncio.run(demonstrate_isolation_modes())
    else:
        print(f"\n运行学校记忆系统示例 (隔离模式: {args.isolation})...")
        asyncio.run(run_example(isolation_mode=args.isolation))

    print("\n示例运行完成!")
    return 0

if __name__ == "__main__":
    sys.exit(main())