-- ai-backend-system/sql/create_deal_table_au.sql
-- 创建 backend_deals_au 表，基于 DealData 模型

-- 先删除所有依赖的视图
DROP VIEW IF EXISTS backend_deals_with_products_au;
DROP VIEW IF EXISTS backend_deals_with_products;

-- 如果表已存在，先删除
DROP TABLE IF EXISTS backend_deal_name_embeddings_au;
DROP TABLE IF EXISTS backend_deals_au;

-- 创建商品分类枚举类型（如果尚未创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'backend_product_category') THEN
        CREATE TYPE backend_product_category AS ENUM (
            'electronics', 'fashion', 'home', 'beauty', 'sports',
            'toys', 'books', 'grocery', 'automotive', 'health', 'other'
        );
    END IF;
END $$;

-- 创建 backend_deals_au 表
CREATE TABLE backend_deals_au (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES backend_products(id),  -- 外键引用全局产品表
    name VARCHAR(255) NOT NULL,
    date TIMESTAMP,
    img_link VARCHAR(255),
    source_site VARCHAR(100),
    current_price DECIMAL(10, 2),
    original_price DECIMAL(10, 2),
    average_price DECIMAL(10, 2),
    original_link VARCHAR(255),
    deal_link VARCHAR(255),
    description TEXT,
    categories backend_product_category[] DEFAULT ARRAY[]::backend_product_category[],
    expiration TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    currency VARCHAR(3) NOT NULL DEFAULT 'AUD'  -- 添加货币字段，默认为澳元
);

-- 创建交易名称嵌入表（使用 pgvector 扩展）
CREATE TABLE backend_deal_name_embeddings_au (
    id SERIAL PRIMARY KEY,
    deal_id INTEGER REFERENCES backend_deals_au(id),
    embedding vector(1536),  -- OpenAI text-embedding-3-small/ada-002 输出 1536 维向量
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX idx_backend_deals_au_name ON backend_deals_au(name);
CREATE INDEX idx_backend_deals_au_date ON backend_deals_au(date);
CREATE INDEX idx_backend_deals_au_source_site ON backend_deals_au(source_site);
CREATE INDEX idx_backend_deals_au_categories ON backend_deals_au USING gin(categories);
CREATE INDEX idx_backend_deals_au_product_id ON backend_deals_au(product_id);  -- 添加产品ID索引

CREATE INDEX idx_backend_deal_name_embeddings_au_embedding ON backend_deal_name_embeddings_au 
USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);

-- 创建触发器以自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_backend_deals_au_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_backend_deals_au_modtime
BEFORE UPDATE ON backend_deals_au
FOR EACH ROW
EXECUTE FUNCTION update_backend_deals_au_modified_column();

-- 创建视图：整合产品信息的澳洲交易视图
CREATE OR REPLACE VIEW backend_deals_with_products_au AS
SELECT 
    d.*,
    p.product_name,
    p.brand,
    p.category as product_category,
    p.sub_category,
    p.asin,
    p.image_url as product_image_url,
    rp.price_normal_180days,
    rp.price_lowest,
    rp.price_average
FROM 
    backend_deals_au d
JOIN 
    backend_products p ON d.product_id = p.id
LEFT JOIN 
    backend_product_region_prices rp ON p.id = rp.product_id AND rp.region = 'au';

-- 添加注释
COMMENT ON TABLE backend_deals_au IS '存储澳大利亚地区交易信息的表，基于 DealData 模型';
COMMENT ON COLUMN backend_deals_au.id IS '主键';
COMMENT ON COLUMN backend_deals_au.product_id IS '关联的全局产品 ID';
COMMENT ON COLUMN backend_deals_au.name IS '交易名称';
COMMENT ON COLUMN backend_deals_au.date IS '交易发布日期和时间';
COMMENT ON COLUMN backend_deals_au.img_link IS '交易图片链接';
COMMENT ON COLUMN backend_deals_au.source_site IS '交易来源网站';
COMMENT ON COLUMN backend_deals_au.current_price IS '当前价格（澳元）';
COMMENT ON COLUMN backend_deals_au.original_price IS '原始价格（澳元）';
COMMENT ON COLUMN backend_deals_au.average_price IS '平均价格（澳元）';
COMMENT ON COLUMN backend_deals_au.original_link IS '原始交易链接';
COMMENT ON COLUMN backend_deals_au.deal_link IS '交易详情页链接';
COMMENT ON COLUMN backend_deals_au.description IS '交易完整描述';
COMMENT ON COLUMN backend_deals_au.categories IS '交易所属类别';
COMMENT ON COLUMN backend_deals_au.expiration IS '交易过期时间';
COMMENT ON COLUMN backend_deals_au.currency IS '货币代码，默认 AUD';

COMMENT ON TABLE backend_deal_name_embeddings_au IS '存储澳大利亚地区交易名称的向量嵌入，用于相似度搜索';
COMMENT ON COLUMN backend_deal_name_embeddings_au.deal_id IS '关联的交易 ID';
COMMENT ON COLUMN backend_deal_name_embeddings_au.embedding IS '交易名称的向量嵌入';

COMMENT ON VIEW backend_deals_with_products_au IS '整合了产品信息的澳洲交易视图'; 