import sqlite3
import os
from pathlib import Path

def create_competition_database(db_path="competition_database.db"):
    """创建竞赛题目数据库和相关表结构"""
    
    # 确保目录存在
    db_dir = os.path.dirname(db_path)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir)
    
    # 连接到数据库（如果不存在则创建）
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建竞赛题目主表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS backend_competition_topics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT,
        competition_name TEXT,
        problem_id TEXT,
        description TEXT,
        input_format TEXT,
        output_format TEXT,
        notes TEXT,
        hints TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        difficulty TEXT,
        tags TEXT,
        metadata TEXT
    )
    ''')
    
    # 创建样例表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS backend_competition_topic_examples (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        topic_id INTEGER,
        input_example TEXT NOT NULL,
        output_example TEXT NOT NULL,
        explanation TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (topic_id) REFERENCES backend_competition_topics(id) ON DELETE CASCADE
    )
    ''')
    
    # 创建数据规模表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS backend_competition_topic_data_scales (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        topic_id INTEGER,
        test_point TEXT NOT NULL,
        scale TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (topic_id) REFERENCES backend_competition_topics(id) ON DELETE CASCADE
    )
    ''')
    
    # 创建向量嵌入表（SQLite不支持向量类型，使用BLOB存储）
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS backend_competition_topic_embeddings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        topic_id INTEGER,
        content TEXT NOT NULL,
        embedding BLOB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (topic_id) REFERENCES backend_competition_topics(id) ON DELETE CASCADE
    )
    ''')
    
    # 创建索引以提高查询性能
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_topics_title ON backend_competition_topics(title)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_topics_problem_id ON backend_competition_topics(problem_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_examples_topic_id ON backend_competition_topic_examples(topic_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_data_scales_topic_id ON backend_competition_topic_data_scales(topic_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_embeddings_topic_id ON backend_competition_topic_embeddings(topic_id)')
    
    # 创建视图：整合样例和数据规模的竞赛题目视图
    cursor.execute('''
    CREATE VIEW IF NOT EXISTS backend_competition_topics_with_examples AS
    SELECT 
        t.*,
        (
            SELECT json_group_array(json_object(
                'id', e.id,
                'input_example', e.input_example,
                'output_example', e.output_example,
                'explanation', e.explanation
            ))
            FROM backend_competition_topic_examples e
            WHERE e.topic_id = t.id
        ) AS examples,
        (
            SELECT json_group_array(json_object(
                'id', ds.id,
                'test_point', ds.test_point,
                'scale', ds.scale
            ))
            FROM backend_competition_topic_data_scales ds
            WHERE ds.topic_id = t.id
        ) AS data_scales
    FROM 
        backend_competition_topics t
    ''')
    
    # 提交更改
    conn.commit()
    conn.close()
    
    print(f"数据库创建成功: {db_path}")
    return db_path