-- ai-backend-system/sql/create_competition_topic_table.sql
-- 创建 backend_competition_topics 表，基于 CompetitionTopicData 模型

-- 先删除所有依赖的视图
DROP VIEW IF EXISTS backend_competition_topics_with_examples;

-- 如果表已存在，先删除
DROP TABLE IF EXISTS backend_competition_topic_examples;
DROP TABLE IF EXISTS backend_competition_topic_data_scales;
DROP TABLE IF EXISTS backend_competition_topic_embeddings;
DROP TABLE IF EXISTS backend_competition_topics;

-- 创建难度级别枚举类型
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'backend_difficulty_level') THEN
        CREATE TYPE backend_difficulty_level AS ENUM (
            '入门', '简单', '中等', '困难', '挑战'
        );
    END IF;
END $$;

-- 创建 backend_competition_topics 表
CREATE TABLE backend_competition_topics (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    competition_name VARCHA<PERSON>(255),
    problem_id VARCHAR(50),
    description TEXT,
    input_format TEXT,
    output_format TEXT,
    notes TEXT,
    hints TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    difficulty backend_difficulty_level,
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    metadata JSONB DEFAULT '{}'::JSONB
);

-- 创建输入输出样例表
CREATE TABLE backend_competition_topic_examples (
    id SERIAL PRIMARY KEY,
    topic_id INTEGER REFERENCES backend_competition_topics(id) ON DELETE CASCADE,
    input_example TEXT NOT NULL,
    output_example TEXT NOT NULL,
    explanation TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建数据规模约定表
CREATE TABLE backend_competition_topic_data_scales (
    id SERIAL PRIMARY KEY,
    topic_id INTEGER REFERENCES backend_competition_topics(id) ON DELETE CASCADE,
    test_point VARCHAR(50) NOT NULL,
    scale VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建向量搜索表（使用 pgvector 扩展）
CREATE EXTENSION IF NOT EXISTS vector;
CREATE TABLE backend_competition_topic_embeddings (
    id SERIAL PRIMARY KEY,
    topic_id INTEGER REFERENCES backend_competition_topics(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    embedding vector(1536),  -- OpenAI text-embedding-3-small/ada-002 输出 1536 维向量
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX idx_backend_competition_topics_title ON backend_competition_topics(title);
CREATE INDEX idx_backend_competition_topics_problem_id ON backend_competition_topics(problem_id);
CREATE INDEX idx_backend_competition_topics_competition_name ON backend_competition_topics(competition_name);
CREATE INDEX idx_backend_competition_topics_difficulty ON backend_competition_topics(difficulty);
CREATE INDEX idx_backend_competition_topics_tags ON backend_competition_topics USING gin(tags);
CREATE INDEX idx_backend_competition_topics_metadata ON backend_competition_topics USING gin(metadata);

CREATE INDEX idx_backend_competition_topic_examples_topic_id ON backend_competition_topic_examples(topic_id);
CREATE INDEX idx_backend_competition_topic_data_scales_topic_id ON backend_competition_topic_data_scales(topic_id);

CREATE INDEX idx_backend_competition_topic_embeddings_embedding ON backend_competition_topic_embeddings 
USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);

-- 创建触发器以自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_backend_competition_topics_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_backend_competition_topics_modtime
BEFORE UPDATE ON backend_competition_topics
FOR EACH ROW
EXECUTE FUNCTION update_backend_competition_topics_modified_column();

-- 创建视图：整合样例和数据规模的竞赛题目视图
CREATE OR REPLACE VIEW backend_competition_topics_with_examples AS
SELECT 
    t.*,
    json_agg(DISTINCT jsonb_build_object(
        'id', e.id,
        'input_example', e.input_example,
        'output_example', e.output_example,
        'explanation', e.explanation
    )) FILTER (WHERE e.id IS NOT NULL) AS examples,
    json_agg(DISTINCT jsonb_build_object(
        'id', ds.id,
        'test_point', ds.test_point,
        'scale', ds.scale
    )) FILTER (WHERE ds.id IS NOT NULL) AS data_scales
FROM 
    backend_competition_topics t
LEFT JOIN 
    backend_competition_topic_examples e ON t.id = e.topic_id
LEFT JOIN 
    backend_competition_topic_data_scales ds ON t.id = ds.topic_id
GROUP BY 
    t.id;

-- 添加注释
COMMENT ON TABLE backend_competition_topics IS '存储竞赛题目信息的表，基于 CompetitionTopicData 模型';
COMMENT ON COLUMN backend_competition_topics.id IS '主键';
COMMENT ON COLUMN backend_competition_topics.title IS '题目标题';
COMMENT ON COLUMN backend_competition_topics.competition_name IS '所属竞赛名称';
COMMENT ON COLUMN backend_competition_topics.problem_id IS '题目编号';
COMMENT ON COLUMN backend_competition_topics.description IS '题目描述';
COMMENT ON COLUMN backend_competition_topics.input_format IS '输入格式';
COMMENT ON COLUMN backend_competition_topics.output_format IS '输出格式';
COMMENT ON COLUMN backend_competition_topics.notes IS '说明/提示';
COMMENT ON COLUMN backend_competition_topics.hints IS '提示';
COMMENT ON COLUMN backend_competition_topics.difficulty IS '难度级别';
COMMENT ON COLUMN backend_competition_topics.tags IS '题目标签';
COMMENT ON COLUMN backend_competition_topics.metadata IS '其他元数据，如来源、时间限制等';

COMMENT ON TABLE backend_competition_topic_examples IS '存储竞赛题目输入输出样例的表';
COMMENT ON COLUMN backend_competition_topic_examples.topic_id IS '关联的竞赛题目ID';
COMMENT ON COLUMN backend_competition_topic_examples.input_example IS '输入样例';
COMMENT ON COLUMN backend_competition_topic_examples.output_example IS '输出样例';
COMMENT ON COLUMN backend_competition_topic_examples.explanation IS '样例解释';

COMMENT ON TABLE backend_competition_topic_data_scales IS '存储竞赛题目数据规模约定的表';
COMMENT ON COLUMN backend_competition_topic_data_scales.topic_id IS '关联的竞赛题目ID';
COMMENT ON COLUMN backend_competition_topic_data_scales.test_point IS '测试点编号';
COMMENT ON COLUMN backend_competition_topic_data_scales.scale IS '数据规模';

COMMENT ON TABLE backend_competition_topic_embeddings IS '存储竞赛题目向量嵌入的表，用于相似度搜索';
COMMENT ON COLUMN backend_competition_topic_embeddings.topic_id IS '关联的竞赛题目ID';
COMMENT ON COLUMN backend_competition_topic_embeddings.content IS '用于生成嵌入的文本内容';
COMMENT ON COLUMN backend_competition_topic_embeddings.embedding IS '题目内容的向量嵌入';

COMMENT ON VIEW backend_competition_topics_with_examples IS '整合了样例和数据规模的竞赛题目视图';

-- 创建用于安全插入竞赛题目的存储过程
CREATE OR REPLACE FUNCTION insert_competition_topic(
    p_title VARCHAR(255),
    p_competition_name VARCHAR(255),
    p_problem_id VARCHAR(50),
    p_description TEXT,
    p_input_format TEXT,
    p_output_format TEXT,
    p_notes TEXT,
    p_hints TEXT,
    p_difficulty backend_difficulty_level,
    p_tags TEXT[],
    p_metadata JSONB,
    p_examples JSONB[],
    p_data_scales JSONB[]
) RETURNS INTEGER AS $$
DECLARE
    v_topic_id INTEGER;
    v_example JSONB;
    v_data_scale JSONB;
BEGIN
    -- 使用事务确保原子性
    START TRANSACTION;
    
    -- 插入竞赛题目基本信息
    INSERT INTO backend_competition_topics (
        title, competition_name, problem_id, description, 
        input_format, output_format, notes, hints, 
        difficulty, tags, metadata
    ) VALUES (
        p_title, p_competition_name, p_problem_id, p_description,
        p_input_format, p_output_format, p_notes, p_hints,
        p_difficulty, p_tags, p_metadata
    ) RETURNING id INTO v_topic_id;
    
    -- 插入输入输出样例
    IF p_examples IS NOT NULL THEN
        FOREACH v_example IN ARRAY p_examples LOOP
            INSERT INTO backend_competition_topic_examples (
                topic_id, input_example, output_example, explanation
            ) VALUES (
                v_topic_id, 
                v_example->>'input_example', 
                v_example->>'output_example', 
                v_example->>'explanation'
            );
        END LOOP;
    END IF;
    
    -- 插入数据规模约定
    IF p_data_scales IS NOT NULL THEN
        FOREACH v_data_scale IN ARRAY p_data_scales LOOP
            INSERT INTO backend_competition_topic_data_scales (
                topic_id, test_point, scale
            ) VALUES (
                v_topic_id, 
                v_data_scale->>'test_point', 
                v_data_scale->>'scale'
            );
        END LOOP;
    END IF;
    
    COMMIT;
    RETURN v_topic_id;
EXCEPTION
    WHEN OTHERS THEN
        -- 发生错误时回滚事务
        ROLLBACK;
        RAISE;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION insert_competition_topic IS '安全插入竞赛题目及其样例和数据规模的存储过程，使用事务确保原子性'; 