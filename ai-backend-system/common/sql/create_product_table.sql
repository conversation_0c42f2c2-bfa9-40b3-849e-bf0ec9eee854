-- ai-backend-system/sql/create_product_table.sql
-- 创建 backend_products 表，基于 ProductData 模型

-- 先删除所有依赖的视图
DROP VIEW IF EXISTS backend_product_prices_view;
DROP VIEW IF EXISTS backend_discounted_products;
DROP VIEW IF EXISTS backend_active_products;
DROP VIEW IF EXISTS backend_deals_with_products_au;
DROP VIEW IF EXISTS backend_deals_with_products;

-- 再删除表
DROP TABLE IF EXISTS backend_product_region_prices;
DROP TABLE IF EXISTS backend_products_embeddings;
DROP TABLE IF EXISTS backend_products;

-- 最后删除类型
DROP TYPE IF EXISTS backend_region CASCADE;
DROP TYPE IF EXISTS backend_product_category CASCADE;

-- 创建商品分类枚举类型
CREATE TYPE backend_product_category AS ENUM (
    'electronics', 'fashion', 'home', 'beauty', 'sports',
    'toys', 'books', 'grocery', 'automotive', 'health', 'other'
);

-- 创建更新后的区域枚举类型
CREATE TYPE backend_region AS ENUM (
    -- 大洋洲
    'au',  -- 澳大利亚
    'nz',  -- 新西兰
    
    -- 北美洲
    'us',  -- 美国
    'ca',  -- 加拿大
    
    -- 欧洲
    'uk',  -- 英国
    'de',  -- 德国
    'fr',  -- 法国
    'it',  -- 意大利
    'es',  -- 西班牙
    'nl',  -- 荷兰
    'se',  -- 瑞典
    
    -- 亚洲
    'jp',  -- 日本
    'kr',  -- 韩国
    'sg',  -- 新加坡
    'my',  -- 马来西亚
    'th',  -- 泰国
    'vn',  -- 越南
    'id',  -- 印度尼西亚
    'ph',  -- 菲律宾
    'in',  -- 印度
    
    -- 中东
    'ae',  -- 阿联酋
    'sa',  -- 沙特阿拉伯
    'tr',  -- 土耳其
    
    -- 南美洲
    'br',  -- 巴西
    'mx',  -- 墨西哥
    'ar',  -- 阿根廷
    'cl'   -- 智利
);

COMMENT ON TYPE backend_region IS '支持的国家/地区代码枚举，包括主要的大洋洲、北美洲、欧洲、亚洲、中东和南美洲国家';

-- 创建 backend_products 表
CREATE TABLE backend_products (
    id SERIAL PRIMARY KEY,
    product_name VARCHAR(255) NOT NULL,
    brand VARCHAR(100),
    description TEXT,
    category backend_product_category NOT NULL,
    sub_category VARCHAR(100),
    asin VARCHAR(50),
    image_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    tags TEXT[] DEFAULT ARRAY[]::TEXT[]
);

-- 创建区域价格信息表
CREATE TABLE backend_product_region_prices (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES backend_products(id),
    region backend_region NOT NULL,
    price_normal_180days DECIMAL(10, 2),
    price_lowest DECIMAL(10, 2),
    price_average DECIMAL(10, 2),
    currency VARCHAR(3) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, region)
);

-- 添加复合索引以优化产品区域价格查询
CREATE INDEX idx_product_region_lookup ON backend_product_region_prices (product_id, region);


-- 创建向量搜索表（使用 pgvector 扩展）
CREATE TABLE backend_products_embeddings (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES backend_products(id),
    content TEXT NOT NULL,
    embedding vector(1536),  -- OpenAI text-embedding-3-small/ada-002 输出 1536 维向量
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_backend_products_product_name ON backend_products(product_name);
CREATE INDEX idx_backend_products_brand ON backend_products(brand);
CREATE INDEX idx_backend_products_category ON backend_products(category);
CREATE INDEX idx_backend_products_sub_category ON backend_products(sub_category);
CREATE INDEX idx_backend_products_asin ON backend_products(asin);
CREATE INDEX idx_backend_products_created_at ON backend_products(created_at);
CREATE INDEX idx_backend_products_tags ON backend_products USING gin(tags);

CREATE INDEX idx_backend_product_region_prices_product ON backend_product_region_prices(product_id);
CREATE INDEX idx_backend_product_region_prices_region ON backend_product_region_prices(region);

CREATE INDEX idx_backend_products_embeddings_embedding ON backend_products_embeddings 
USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);

-- 创建触发器
CREATE OR REPLACE FUNCTION update_backend_products_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_backend_products_modtime
BEFORE UPDATE ON backend_products
FOR EACH ROW
EXECUTE FUNCTION update_backend_products_modified_column();

CREATE OR REPLACE FUNCTION update_backend_product_region_prices_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_backend_product_region_prices_modtime
BEFORE UPDATE ON backend_product_region_prices
FOR EACH ROW
EXECUTE FUNCTION update_backend_product_region_prices_modified_column();

-- 创建视图
CREATE OR REPLACE VIEW backend_active_products AS
SELECT p.*, rp.region, rp.price_normal_180days, rp.price_lowest, rp.price_average, rp.currency
FROM backend_products p
LEFT JOIN backend_product_region_prices rp ON p.id = rp.product_id
WHERE p.is_active = TRUE;

CREATE OR REPLACE VIEW backend_discounted_products AS
SELECT 
    p.*,
    rp.region,
    rp.currency,
    rp.price_normal_180days,
    rp.price_lowest,
    rp.price_average,
    rp.price_normal_180days - rp.price_lowest AS discount_amount,
    CASE 
        WHEN rp.price_normal_180days > 0 
        THEN ROUND(((rp.price_normal_180days - rp.price_lowest) / rp.price_normal_180days) * 100, 2)
        ELSE 0
    END AS discount_percentage
FROM 
    backend_products p
JOIN 
    backend_product_region_prices rp ON p.id = rp.product_id
WHERE 
    p.is_active = TRUE AND
    rp.price_lowest < rp.price_normal_180days;

-- 添加示例数据
INSERT INTO backend_products (product_name, brand, description, category, sub_category, asin, image_url, tags)
VALUES
    ('Bose QuietComfort 45', 'Bose', '舒适的降噪耳机', 'electronics', 'audio', 'B098FKXT8L', 
     'https://example.com/images/qc45.jpg', 
     ARRAY['noise-cancelling', 'wireless', 'headphones', 'premium']);

INSERT INTO backend_product_region_prices 
    (product_id, region, price_normal_180days, price_lowest, price_average, currency)
VALUES
    (1, 'au', 329.00, 279.00, 300.00, 'AUD'),
    (1, 'us', 249.00, 199.00, 220.00, 'USD');

-- 创建便捷视图用于产品价格查询
CREATE OR REPLACE VIEW backend_product_prices_view AS
SELECT 
    p.id AS product_id,
    p.product_name,
    p.brand,
    p.category,
    rp.region,
    rp.price_normal_180days,
    rp.price_lowest,
    rp.price_average,
    rp.currency,
    CASE 
        WHEN rp.price_normal_180days > 0 AND rp.price_lowest < rp.price_normal_180days
        THEN ROUND(((rp.price_normal_180days - rp.price_lowest) / rp.price_normal_180days * 100), 2)
        ELSE 0
    END AS discount_percentage
FROM 
    backend_products p
LEFT JOIN 
    backend_product_region_prices rp ON p.id = rp.product_id;

-- 添加注释
COMMENT ON TABLE backend_products IS '存储产品基本信息的表';
COMMENT ON TABLE backend_product_region_prices IS '存储产品区域价格信息的表';
COMMENT ON TABLE backend_products_embeddings IS '存储产品文本内容的向量嵌入';

COMMENT ON VIEW backend_active_products IS '当前活跃的产品及其区域价格信息';
COMMENT ON VIEW backend_discounted_products IS '当前有折扣的产品及其区域价格信息';
COMMENT ON VIEW backend_product_prices_view IS '产品价格查询视图，用于快速获取产品在不同区域的价格信息';

-- 多区域并发访问锁策略
COMMENT ON TABLE backend_products IS '存储产品基本信息的全局表，多区域共享。更新操作应使用行级锁并在短事务中执行';
COMMENT ON TABLE backend_product_region_prices IS '存储产品区域价格信息的表。区域特定操作使用(product_id, region)复合条件以减少锁争用';

-- 创建用于安全更新的存储过程
CREATE OR REPLACE FUNCTION update_product_with_region_prices(
    p_product_id INTEGER,
    p_product_name VARCHAR(255),
    p_brand VARCHAR(100),
    p_description TEXT,
    p_category backend_product_category,
    p_sub_category VARCHAR(100),
    p_region backend_region,
    p_price_normal DECIMAL(10, 2),
    p_price_lowest DECIMAL(10, 2),
    p_price_average DECIMAL(10, 2),
    p_currency VARCHAR(3)
) RETURNS VOID AS $$
BEGIN
    -- 使用事务确保原子性
    START TRANSACTION;
    
    -- 更新产品基本信息（使用行级锁）
    UPDATE backend_products
    SET 
        product_name = p_product_name,
        brand = p_brand,
        description = p_description,
        category = p_category,
        sub_category = p_sub_category,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_product_id;
    
    -- 更新或插入区域价格（使用行级锁）
    INSERT INTO backend_product_region_prices
        (product_id, region, price_normal_180days, price_lowest, price_average, currency)
    VALUES
        (p_product_id, p_region, p_price_normal, p_price_lowest, p_price_average, p_currency)
    ON CONFLICT (product_id, region) 
    DO UPDATE SET
        price_normal_180days = p_price_normal,
        price_lowest = p_price_lowest,
        price_average = p_price_average,
        currency = p_currency,
        updated_at = CURRENT_TIMESTAMP;
    
    COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        -- 发生错误时回滚事务
        ROLLBACK;
        RAISE;
END;
$$ LANGUAGE plpgsql;

-- 创建用于安全读取的函数
CREATE OR REPLACE FUNCTION get_product_with_region_price(
    p_product_id INTEGER,
    p_region backend_region
) RETURNS TABLE (
    id INTEGER,
    product_name VARCHAR(255),
    brand VARCHAR(100),
    description TEXT,
    category backend_product_category,
    sub_category VARCHAR(100),
    region backend_region,
    price_normal_180days DECIMAL(10, 2),
    price_lowest DECIMAL(10, 2),
    price_average DECIMAL(10, 2),
    currency VARCHAR(3)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.product_name,
        p.brand,
        p.description,
        p.category,
        p.sub_category,
        rp.region,
        rp.price_normal_180days,
        rp.price_lowest,
        rp.price_average,
        rp.currency
    FROM 
        backend_products p
    LEFT JOIN 
        backend_product_region_prices rp 
        ON p.id = rp.product_id AND rp.region = p_region
    WHERE 
        p.id = p_product_id;
END;
$$ LANGUAGE plpgsql;

-- 使用示例
COMMENT ON FUNCTION update_product_with_region_prices IS '安全更新产品及其区域价格的存储过程，使用事务和行级锁';
COMMENT ON FUNCTION get_product_with_region_price IS '安全读取产品及其特定区域价格的函数';
