-- 教学机构（租户）表
CREATE TABLE backend_institutions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',  -- active, suspended, expired
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    valid_from TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    valid_until TIMESTAMP,
    max_subjects INTEGER NOT NULL DEFAULT 5,
    max_students INTEGER NOT NULL DEFAULT 100,
    current_subjects INTEGER NOT NULL DEFAULT 0,
    current_students INTEGER NOT NULL DEFAULT 0,
    token_quota INTEGER NOT NULL DEFAULT 1000000,
    token_used INTEGER NOT NULL DEFAULT 0,
    question_quota INTEGER NOT NULL DEFAULT 10000,
    questions_asked INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_backend_institutions_status ON backend_institutions(status);
CREATE INDEX idx_backend_institutions_valid_until ON backend_institutions(valid_until);

-- 科目表
CREATE TABLE backend_subjects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    institution_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',  -- active, suspended, expired
    valid_from TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    valid_until TIMESTAMP,
    max_students INTEGER NOT NULL DEFAULT 30,
    current_students INTEGER NOT NULL DEFAULT 0,
    token_quota INTEGER NOT NULL DEFAULT 100000,
    token_used INTEGER NOT NULL DEFAULT 0,
    question_quota INTEGER NOT NULL DEFAULT 1000,
    questions_asked INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (institution_id) REFERENCES backend_institutions(id) ON DELETE CASCADE,
    UNIQUE (institution_id, name)
);

-- 创建索引
CREATE INDEX idx_backend_subjects_institution_id ON backend_subjects(institution_id);
CREATE INDEX idx_backend_subjects_status ON backend_subjects(status);
CREATE INDEX idx_backend_subjects_valid_until ON backend_subjects(valid_until);

-- 学生表 (基本信息)
CREATE TABLE backend_students (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    wechat_id VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (wechat_id)
);

-- 创建索引
CREATE INDEX idx_backend_students_wechat_id ON backend_students(wechat_id);

-- 学生-机构关联表 (多对多关系)
CREATE TABLE backend_student_institutions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    institution_id INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',  -- active, suspended, expired
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES backend_students(id) ON DELETE CASCADE,
    FOREIGN KEY (institution_id) REFERENCES backend_institutions(id) ON DELETE CASCADE,
    UNIQUE (student_id, institution_id)
);

-- 创建索引
CREATE INDEX idx_backend_student_institutions_student_id ON backend_student_institutions(student_id);
CREATE INDEX idx_backend_student_institutions_institution_id ON backend_student_institutions(institution_id);

-- 学生-科目注册表 (学生在某机构下报名的某科目)
CREATE TABLE backend_student_enrollments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    token_quota INTEGER NOT NULL DEFAULT 10000,
    token_used INTEGER NOT NULL DEFAULT 0,
    question_quota INTEGER NOT NULL DEFAULT 100,
    questions_asked INTEGER NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'active',  -- active, suspended, expired
    enrolled_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    valid_until TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES backend_students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES backend_subjects(id) ON DELETE CASCADE,
    UNIQUE (student_id, subject_id)
);

-- 创建索引
CREATE INDEX idx_backend_student_enrollments_student_id ON backend_student_enrollments(student_id);
CREATE INDEX idx_backend_student_enrollments_subject_id ON backend_student_enrollments(subject_id);
CREATE INDEX idx_backend_student_enrollments_status ON backend_student_enrollments(status);

-- 操作日志表 (用于处理并发问题)
CREATE TABLE backend_education_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    action VARCHAR(50) NOT NULL,  -- increment_token, increment_question, etc.
    entity_type VARCHAR(50) NOT NULL,  -- institution, subject, enrollment
    entity_id INTEGER NOT NULL,
    change_amount INTEGER NOT NULL,
    previous_value INTEGER,
    new_value INTEGER,
    performed_by VARCHAR(100),
    performed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_backend_education_logs_entity_type_id ON backend_education_logs(entity_type, entity_id);
CREATE INDEX idx_backend_education_logs_performed_at ON backend_education_logs(performed_at);

-- 触发器：更新机构的当前科目数
CREATE TRIGGER after_backend_subject_insert
AFTER INSERT ON backend_subjects
BEGIN
    UPDATE backend_institutions 
    SET current_subjects = current_subjects + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.institution_id;
END;

CREATE TRIGGER after_backend_subject_delete
AFTER DELETE ON backend_subjects
BEGIN
    UPDATE backend_institutions 
    SET current_subjects = current_subjects - 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.institution_id;
END;

-- 触发器：更新科目和机构的当前学生数
CREATE TRIGGER after_backend_enrollment_insert
AFTER INSERT ON backend_student_enrollments
BEGIN
    -- 更新科目的学生数
    UPDATE backend_subjects 
    SET current_students = current_students + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.subject_id;
    
    -- 更新机构的学生数 (如果这个学生是首次在该机构注册)
    UPDATE backend_institutions 
    SET current_students = (
        SELECT COUNT(DISTINCT s.student_id) 
        FROM backend_student_enrollments s
        JOIN backend_subjects sub ON s.subject_id = sub.id
        WHERE sub.institution_id = (
            SELECT institution_id FROM backend_subjects WHERE id = NEW.subject_id
        )
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = (
        SELECT institution_id FROM backend_subjects WHERE id = NEW.subject_id
    );
END;

CREATE TRIGGER after_backend_enrollment_delete
AFTER DELETE ON backend_student_enrollments
BEGIN
    -- 更新科目的学生数
    UPDATE backend_subjects 
    SET current_students = current_students - 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.subject_id;
    
    -- 更新机构的学生数
    UPDATE backend_institutions 
    SET current_students = (
        SELECT COUNT(DISTINCT s.student_id) 
        FROM backend_student_enrollments s
        JOIN backend_subjects sub ON s.subject_id = sub.id
        WHERE sub.institution_id = (
            SELECT institution_id FROM backend_subjects WHERE id = OLD.subject_id
        )
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = (
        SELECT institution_id FROM backend_subjects WHERE id = OLD.subject_id
    );
END;

-- 视图：教育机构使用统计
CREATE VIEW backend_institution_usage_stats AS
SELECT 
    i.id,
    i.name,
    i.status,
    i.valid_until,
    i.max_subjects,
    i.current_subjects,
    i.max_students,
    i.current_students,
    i.token_quota,
    i.token_used,
    (i.token_quota - i.token_used) AS remaining_tokens,
    i.question_quota,
    i.questions_asked,
    (i.question_quota - i.questions_asked) AS remaining_questions,
    CASE 
        WHEN i.valid_until IS NULL THEN 'No Expiration'
        WHEN i.valid_until > CURRENT_TIMESTAMP THEN 'Active'
        ELSE 'Expired'
    END AS subscription_status
FROM 
    backend_institutions i;

-- 视图：学生参与情况
CREATE VIEW backend_student_activity AS
SELECT 
    s.id AS student_id,
    s.name AS student_name,
    s.wechat_id,
    i.id AS institution_id,
    i.name AS institution_name,
    sub.id AS subject_id,
    sub.name AS subject_name,
    e.questions_asked,
    e.question_quota,
    e.token_used,
    e.token_quota,
    e.status,
    e.valid_until,
    CASE 
        WHEN e.valid_until IS NULL THEN 'No Expiration'
        WHEN e.valid_until > CURRENT_TIMESTAMP THEN 'Active'
        ELSE 'Expired'
    END AS enrollment_status
FROM 
    backend_students s
JOIN 
    backend_student_enrollments e ON s.id = e.student_id
JOIN 
    backend_subjects sub ON e.subject_id = sub.id
JOIN 
    backend_institutions i ON sub.institution_id = i.id;

-- 存储过程：安全增加token使用量（处理并发）
CREATE PROCEDURE backend_increment_token_usage(
    IN p_entity_type VARCHAR(50),
    IN p_entity_id INTEGER,
    IN p_amount INTEGER,
    IN p_performed_by VARCHAR(100)
)
BEGIN
    -- 开始事务
    BEGIN TRANSACTION;
    
    CASE p_entity_type
        WHEN 'institution' THEN
            -- 检查配额
            SELECT token_quota, token_used INTO @quota, @used FROM backend_institutions WHERE id = p_entity_id FOR UPDATE;
            
            IF @used + p_amount <= @quota THEN
                -- 更新使用量
                UPDATE backend_institutions 
                SET token_used = token_used + p_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = p_entity_id;
                
                -- 记录日志
                INSERT INTO backend_education_logs (
                    action, entity_type, entity_id, change_amount, 
                    previous_value, new_value, performed_by
                ) VALUES (
                    'increment_token', p_entity_type, p_entity_id, p_amount,
                    @used, @used + p_amount, p_performed_by
                );
                
                SELECT 'success' AS result;
            ELSE
                SELECT 'quota_exceeded' AS result;
            END IF;
            
        WHEN 'subject' THEN
            -- 检查配额
            SELECT token_quota, token_used INTO @quota, @used FROM backend_subjects WHERE id = p_entity_id FOR UPDATE;
            
            IF @used + p_amount <= @quota THEN
                -- 更新科目使用量
                UPDATE backend_subjects 
                SET token_used = token_used + p_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = p_entity_id;
                
                -- 获取机构ID并更新机构使用量
                SELECT institution_id INTO @inst_id FROM backend_subjects WHERE id = p_entity_id;
                
                UPDATE backend_institutions 
                SET token_used = token_used + p_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = @inst_id;
                
                -- 记录日志
                INSERT INTO backend_education_logs (
                    action, entity_type, entity_id, change_amount, 
                    previous_value, new_value, performed_by
                ) VALUES (
                    'increment_token', p_entity_type, p_entity_id, p_amount,
                    @used, @used + p_amount, p_performed_by
                );
                
                SELECT 'success' AS result;
            ELSE
                SELECT 'quota_exceeded' AS result;
            END IF;
            
        WHEN 'enrollment' THEN
            -- 检查配额
            SELECT token_quota, token_used INTO @quota, @used FROM backend_student_enrollments WHERE id = p_entity_id FOR UPDATE;
            
            IF @used + p_amount <= @quota THEN
                -- 更新学生注册记录的使用量
                UPDATE backend_student_enrollments 
                SET token_used = token_used + p_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = p_entity_id;
                
                -- 获取科目ID
                SELECT subject_id INTO @subj_id FROM backend_student_enrollments WHERE id = p_entity_id;
                
                -- 更新科目使用量
                UPDATE backend_subjects 
                SET token_used = token_used + p_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = @subj_id;
                
                -- 获取机构ID并更新机构使用量
                SELECT institution_id INTO @inst_id FROM backend_subjects WHERE id = @subj_id;
                
                UPDATE backend_institutions 
                SET token_used = token_used + p_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = @inst_id;
                
                -- 记录日志
                INSERT INTO backend_education_logs (
                    action, entity_type, entity_id, change_amount, 
                    previous_value, new_value, performed_by
                ) VALUES (
                    'increment_token', p_entity_type, p_entity_id, p_amount,
                    @used, @used + p_amount, p_performed_by
                );
                
                SELECT 'success' AS result;
            ELSE
                SELECT 'quota_exceeded' AS result;
            END IF;
            
        ELSE
            SELECT 'invalid_entity_type' AS result;
    END CASE;
    
    -- 提交事务
    COMMIT;
END;

-- 存储过程：安全增加问题数（处理并发）
CREATE PROCEDURE backend_increment_question_count(
    IN p_entity_type VARCHAR(50),
    IN p_entity_id INTEGER,
    IN p_amount INTEGER,
    IN p_performed_by VARCHAR(100)
)
BEGIN
    -- 开始事务
    BEGIN TRANSACTION;
    
    CASE p_entity_type
        WHEN 'institution' THEN
            -- 检查配额
            SELECT question_quota, questions_asked INTO @quota, @used FROM backend_institutions WHERE id = p_entity_id FOR UPDATE;
            
            IF @used + p_amount <= @quota THEN
                -- 更新使用量
                UPDATE backend_institutions 
                SET questions_asked = questions_asked + p_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = p_entity_id;
                
                -- 记录日志
                INSERT INTO backend_education_logs (
                    action, entity_type, entity_id, change_amount, 
                    previous_value, new_value, performed_by
                ) VALUES (
                    'increment_question', p_entity_type, p_entity_id, p_amount,
                    @used, @used + p_amount, p_performed_by
                );
                
                SELECT 'success' AS result;
            ELSE
                SELECT 'quota_exceeded' AS result;
            END IF;
            
        WHEN 'subject' THEN
            -- 检查配额
            SELECT question_quota, questions_asked INTO @quota, @used FROM backend_subjects WHERE id = p_entity_id FOR UPDATE;
            
            IF @used + p_amount <= @quota THEN
                -- 更新科目使用量
                UPDATE backend_subjects 
                SET questions_asked = questions_asked + p_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = p_entity_id;
                
                -- 获取机构ID并更新机构使用量
                SELECT institution_id INTO @inst_id FROM backend_subjects WHERE id = p_entity_id;
                
                UPDATE backend_institutions 
                SET questions_asked = questions_asked + p_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = @inst_id;
                
                -- 记录日志
                INSERT INTO backend_education_logs (
                    action, entity_type, entity_id, change_amount, 
                    previous_value, new_value, performed_by
                ) VALUES (
                    'increment_question', p_entity_type, p_entity_id, p_amount,
                    @used, @used + p_amount, p_performed_by
                );
                
                SELECT 'success' AS result;
            ELSE
                SELECT 'quota_exceeded' AS result;
            END IF;
            
        WHEN 'enrollment' THEN
            -- 检查配额
            SELECT question_quota, questions_asked INTO @quota, @used FROM backend_student_enrollments WHERE id = p_entity_id FOR UPDATE;
            
            IF @used + p_amount <= @quota THEN
                -- 更新学生注册记录的使用量
                UPDATE backend_student_enrollments 
                SET questions_asked = questions_asked + p_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = p_entity_id;
                
                -- 获取科目ID
                SELECT subject_id INTO @subj_id FROM backend_student_enrollments WHERE id = p_entity_id;
                
                -- 更新科目使用量
                UPDATE backend_subjects 
                SET questions_asked = questions_asked + p_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = @subj_id;
                
                -- 获取机构ID并更新机构使用量
                SELECT institution_id INTO @inst_id FROM backend_subjects WHERE id = @subj_id;
                
                UPDATE backend_institutions 
                SET questions_asked = questions_asked + p_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = @inst_id;
                
                -- 记录日志
                INSERT INTO backend_education_logs (
                    action, entity_type, entity_id, change_amount, 
                    previous_value, new_value, performed_by
                ) VALUES (
                    'increment_question', p_entity_type, p_entity_id, p_amount,
                    @used, @used + p_amount, p_performed_by
                );
                
                SELECT 'success' AS result;
            ELSE
                SELECT 'quota_exceeded' AS result;
            END IF;
            
        ELSE
            SELECT 'invalid_entity_type' AS result;
    END CASE;
    
    -- 提交事务
    COMMIT;
END;

-- 添加机构使用统计视图 (与education_db_manager.py中使用的一致)
CREATE VIEW IF NOT EXISTS backend_institution_usage_stats AS
SELECT 
    i.id,
    i.name,
    i.description,
    i.contact_person,
    i.contact_email,
    i.contact_phone,
    i.max_subjects,
    (SELECT COUNT(*) FROM backend_subjects WHERE institution_id = i.id AND status = 'active') AS current_subjects,
    i.max_students,
    (SELECT COUNT(DISTINCT student_id) FROM backend_student_institutions WHERE institution_id = i.id AND status = 'active') AS current_students,
    i.token_quota,
    i.token_used,
    i.question_quota,
    i.questions_asked,
    i.status,
    i.valid_until,
    i.created_at,
    i.updated_at
FROM backend_institutions i; 