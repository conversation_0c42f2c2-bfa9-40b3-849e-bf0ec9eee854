# ai-backend-system/config/__init__.py
"""
config 包的初始化模块

该模块负责加载和管理系统配置参数，提供统一的配置访问方式。
配置值通过 get_config()['section']['subsection'] 格式访问。
"""
import os
import yaml
from typing import Dict, Any

# 配置文件路径
_CONFIG_FILE_PATH = os.path.join(os.path.dirname(__file__), 'config.yaml')

# 缓存配置数据
_config_cache = None

def get_config() -> Dict[str, Any]:
    """
    获取配置信息

    从配置文件加载配置信息，并缓存结果以提高性能。
    配置值可以通过 get_config()['section']['subsection'] 格式访问。

    例如：
    - 获取日志级别: get_config()['logging']['log_level']
    - 获取企业微信Token: get_config()['wecom']['Token']
    - 获取代理模型名称: get_config()['agents']['memory_system']['agent_model_name']

    Returns:
        Dict[str, Any]: 包含所有配置信息的字典
    """
    global _config_cache

    # 如果配置已缓存，直接返回
    if _config_cache is not None:
        return _config_cache

    try:
        # 加载配置文件
        with open(_CONFIG_FILE_PATH, 'r', encoding='utf-8') as f:
            _config_cache = yaml.safe_load(f) or {}
        return _config_cache
    except FileNotFoundError:
        print(f"配置文件未找到: {_CONFIG_FILE_PATH}")
        # 返回空字典，确保可以使用 get_config()['section'] 格式访问，即使配置文件不存在
        return {}
    except yaml.YAMLError as e:
        print(f"配置文件解析错误: {_CONFIG_FILE_PATH} - {e}")
        # 返回空字典，确保可以使用 get_config()['section'] 格式访问，即使配置文件解析错误
        return {}