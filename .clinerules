

















# AutoGen Framework Standards
- Must use AutoGen 0.4 API (reference: https://microsoft.github.com/autogen/stable//reference/index.html)
- Prohibited from using v0.2 import methods
- Required imports include: `from autogen_agentchat.agents import AssistantAgent` and `from autogen_ext.models.openai import OpenAIChatCompletionClient`
- Must use async/await syntax for asynchronous operations
- Must properly handle CancellationToken
- Must use message types defined in autogen_agentchat.messages

# Agent Configuration
- Agents must inherit from autogen_agentchat.agents base classes
- Agent initialization requires name, system_message, and model_client
- Default client must use Gemini with specific configuration:
  ```python
  model_client = OpenAIChatCompletionClient(
      model="gemini-2.0-flash-flash-exp", 
      api_key=os.getenv('GEMINI_API_KEY'),
      model_info={"vision": False, "function_calling": True, "json_output": True, "family": "unknown"}
  )
  ```
- Tools must be integrated using the v0.4 approach via the tools parameter
- Tool functions must have clear type annotations and be async-defined
- User prefers implementing code based on responsibilities and core business processes described in file headers, specifically for teaching assistant agent implementation.

# Web Scraping Configuration
- Must use scrape.do proxy service with specific URL configuration pattern
- BrowserConfig must include proper headers and user agent
- CrawlerRunConfig must specify parameters like word_count_threshold, cache mode, etc.
- Proxy URL configuration must not automatically add render=True

# Project Structure and Management
- Configuration must use ai-backend-system/config/config.yaml
- Configuration should be retrieved using get_config() function from ai-backend-system/config/config.yaml file.
- Access configuration values using direct dictionary indexing with get_config()['section']['subsection'] rather than using get() method.
- When modifying code, use direct dictionary indexing with get_config()['section']['subsection'] rather than using get() method.
- Code should prioritize config file values over hardcoded values
- Sensitive information should use environment variables
- Must implement proper error handling with AutoGen exception types
- Must use autogen_agentchat.state for state management
- Must include clear documentation with parameter types and examples

# Database Standards
- All tables must use 'backend_' prefix and follow snake_case format
- Example tables: backend_products, backend_user_preferences, backend_search_history
- Views must follow the same naming convention
- Project uses Chroma DB for database connections
- DB connector implementations are in ai-backend-system/utils/db/ directory

# Logging Standards
- Logging must be configured in ai-backend-system/main.py
- Logs must be written to ai-backend-system/logs/ directory
- Log filenames must follow format: ai_backend_system.log, scrapy_crawler.log, agent_runtime.log, api_access.log
- Logs must include timestamp, log level, module name, and message
- Logs must be rotated and compressed on a regular basis
- Logs must be monitored and analyzed for errors, performance, and security

# Configuration Management
- Configuration must be managed in ai-backend-system/config/config.yaml
- Configuration must be loaded using ConfigLoader class from ai-backend-system/config/__init__.py
- Configuration must be accessed using get_config() function from ai-backend-system/config/__init__.py
- Configuration must be validated and documented
- Configuration must be versioned and managed in a separate repository

# Unit Testing
- Unit tests must be written for all code
- Unit tests must be located in ai-backend-system/tests/ directory
- Unit tests must be run using pytest
- Unit tests must cover all code paths
- Unit tests should cover multi-user isolation scenarios to ensure proper data separation between users.
- Unit test code should be appended at the end of each file implementation.
- When running Python unittest on files in the project, tests may not be discovered properly and require proper test class structure or naming conventions.
- User prefers comprehensive unit testing for code implementations.

# WeCom Service Implementation
- When implementing business logic for files in @ai-backend-system/services/wecom/, create new complete code based on business descriptions in comments rather than modifying existing code examples.

# Project settings
- Project path has been added to PYTHONPATH in ai-backend-system/

# Conda environment
- Conda environment has been created and activated
- Conda environment name: wiseandluck
- Conda environment has been installed with required packages
- Conda environment has been activated in VSCode
- Conda environment has been activated in terminal
- Conda environment has been activated in Jupyter Notebook
- Conda environment has been activated in PyCharm
- Conda environment has been activated in PyCharm Professional