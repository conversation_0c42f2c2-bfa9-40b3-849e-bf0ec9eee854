# 记忆管理与提示生成重构方案

## 目标：

1.  确保用户记忆（短期、中期、长期）按照用户定义的规则（基于时间或消息数量进行**总结性压缩**）正确地进行压缩。
2.  简化 `TeachingAssistantAgent` 中生成 LLM 提示的逻辑，使其**直接利用所有短期和中期记忆**，并严格按照时间顺序（有近及远）组织，同时**优先考虑当前用户问题**。

---

## 1. 记忆压缩逻辑分析与改进方案

### 核心问题：
`view_user_memory.py` 显示没有中期和长期记忆，表明记忆压缩功能未按预期工作，且用户强调压缩应是“总结描述”而非逐条移动。

### 分析与改进：

`TeachingAssistantAgent` 中的 `_check_and_trigger_compression` 方法负责触发压缩，并调用 `MultiUserMemory.compress_memories`。真正的压缩逻辑（即如何将多条记忆总结为一条）应该在 `MultiUserMemory` 或其内部调用的 `ChromaDBHierarchicalMemory` 中实现。

**关键点：**
`MultiUserMemory` 的 `compress_memories` 方法内部应该调用了 `llm_client` 来执行实际的总结。在 `TeachingAssistantAgent` 的 `_init_memory_system` 方法中，`MultiUserMemoryConfig` 已经配置了 `llm_client` 和 `enable_memory_compression=True`。同时，`_set_custom_compression_prompts_sync` 也设置了压缩提示。

### 行动计划（针对压缩）：

1.  **验证 `MultiUserMemory` 内部逻辑**：
    *   **假设**：`MultiUserMemory` 的 `compress_memories` 方法会：
        *   从源记忆级别（例如，短期记忆）检索符合压缩条件的记忆（例如，超过7天或50条消息）。
        *   将这些记忆的内容作为输入，连同预定义的压缩提示（例如，`short_to_medium_prompt`），发送给配置的 `llm_client`。
        *   LLM 返回一个总结性的文本。
        *   将这个总结性文本作为一条新的记忆，添加到目标记忆级别（例如，中期记忆）的 ChromaDB 集合中，并附带相应的元数据（如 `memory_level`）。
        *   成功添加总结记忆后，从源记忆级别删除原始的、已被总结的记忆。
    *   **如果此假设不成立**：则需要深入 `ai-backend-system/utils/memory/multi_user_memory.py` 和 `ai-backend-system/utils/memory/chromadb_hierarchical_memory.py` 进行修改，以实现上述总结逻辑。

2.  **检查 `_check_and_trigger_compression` 的触发频率和阈值**：
    *   **`maintenance_interval`**：在 `TeachingAssistantAgent` 的 `_init_memory_system` 中配置。这个值决定了 `MultiUserMemory` 内部维护任务（包括压缩）的运行频率。
    *   **`short_to_medium_count` / `medium_to_long_count`**：这些阈值在 `_init_memory_system` 中从配置加载。需要确保这些值与实际的对话量相符，以便触发压缩。
    *   **`short_to_medium_time` / `medium_to_long_time`**：这些时间阈值也从配置加载。同样需要确保它们与实际的对话时间跨度相符。

3.  **日志增强**：
    *   在 `_check_and_trigger_compression` 方法中，增加更详细的日志，记录：
        *   每次检查时当前用户的 `message_count` 和最新消息的时间戳。
        *   是否满足压缩条件。
        *   调用 `self.memory.compress_memories` 的具体参数。
        *   `compress_memories` 返回的结果（成功或失败）。

### 记忆压缩流程图：

```mermaid
graph TD
    A["定时任务/消息处理结束"] --> B{"检查用户记忆是否达到压缩阈值?"}
    B -- 是 --> C{"识别待压缩的短期记忆"}
    C --> D{"调用 LLM 生成总结性中期记忆"}
    D --> E{"将总结性中期记忆存入中期记忆库"}
    E --> F{"从短期记忆库删除原始记忆"}
    F --> G["压缩完成"]
    B -- 否 --> G
    D -- LLM总结失败 --> H["记录错误，不删除原始记忆"]
    H --> G
```

---

## 2. 提示生成逻辑简化与重构方案

### 核心问题：
用户希望简化提示生成逻辑，直接使用所有短期和中期记忆，并强调时间顺序（有近及远），同时将【当前用户问题】提升到所有记忆查询之前，并在系统消息的 prompt 中描述清楚 agent 应该先看清本次用户的提问，并参考短期和中期记忆生成最终回复。

### 分析与改进：

用户明确指出将当前用户问题置于记忆之前，并调整系统消息。

### 新的提示结构：

```
[系统消息/教学提示 - 包含优先处理当前问题的指令]

[当前用户问题]

[所有短期记忆（按时间倒序，最近的在前）]
（每条记忆包含角色和内容，例如“用户: [内容]”或“助手: [内容]”）

[所有中期记忆（按时间倒序，最近的在前）]
（每条记忆包含总结性内容）

[LLM 回复指令]
```

### 具体实现步骤：

1.  **修改 `handle_user_message` 中的记忆查询**：
    *   删除 `short_term_query`, `topic_query`, `concept_query` 的多层次查询。
    *   改为直接查询所有 `MemoryLevel.SHORT_TERM` 的记忆和所有 `MemoryLevel.MEDIUM_TERM` 的记忆。
    *   将这些记忆合并到一个列表中，并作为 `memory_results` 传递给 `_build_response_prompt`。

2.  **重构 `_build_response_prompt` 方法**：

    *   **调整系统消息**：修改 `TeachingAssistantAgent` 的 `system_message`，明确指示 LLM 优先处理当前用户问题，然后参考历史记忆。
        *   **修改点**：在 `TeachingAssistantAgent` 的 `__init__` 方法中，或者在 `_generate_response` 方法中构建 `SystemMessage` 时，修改 `system_message` 的内容。

    *   **移除所有复杂分类和分组逻辑**：删除 `_build_response_prompt` 中所有关于 `short_term_memories`, `topic_related_memories`, `concept_related_memories` 的初始化、填充、以及 `user_memories`, `assistant_memories`, `other_memories` 的分离和平衡逻辑。同时，删除之前添加的基于 `message_pair_id` 的复杂分组和配对逻辑。

    *   **统一处理所有短期和中期记忆**：
        *   从 `memory_results.results` 中筛选出 `memory_level` 为 "short_term" 和 "medium_term" 的记忆。
        *   将这些记忆合并到一个列表中。
        *   **按时间倒序排序**：对合并后的记忆列表，根据 `created_at` 时间戳进行倒序排序（最近的在前）。
        *   **格式化并添加到提示**：遍历排序后的记忆列表。
            *   对于短期记忆（用户或助手消息），以“用户: [内容]”或“助手: [内容]”的格式添加到提示中。
            *   对于中期记忆（总结性内容），直接添加其内容。
            *   限制每条记忆的长度，避免提示过长。

    *   **调整提示构建顺序**：
        *   `prompt` 的开头直接添加当前用户问题 (`user_message`)。
        *   然后，再添加所有短期记忆和中期记忆。

    *   **保留其他部分**：保留题目信息 (`problem_info`) 和 LLM 回复指令的构建逻辑。

### 提示生成流程图：

```mermaid
graph TD
    A["用户消息进入 handle_user_message"] --> B{"查询所有短期记忆"}
    A --> C{"查询所有中期记忆"}
    subgraph 构建提示
        D["系统消息 (包含优先处理当前问题的指令)"] --> E["添加当前用户问题"]
        B & C --> F["合并并按时间倒序排序所有短期和中期记忆"]
        E --> F
        F --> G["添加 LLM 回复指令"]
    end
    G --> H["调用 _generate_response"]